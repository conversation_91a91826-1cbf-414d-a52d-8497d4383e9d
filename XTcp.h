#pragma once
#include <string>

// DLL导出声明
#ifdef WIN32
    #ifdef XTCP_EXPORTS
        #define XTCP_API __declspec(dllexport)
    #else
        #define XTCP_API __declspec(dllimport)
    #endif
#else
    #define XTCP_API
#endif

class XTCP_API XTcp
{
private:
 
public:
int CreateSocket();
bool Bind(unsigned short port);
bool Connect(const char *ip,unsigned short port,int timeoutms = 1000);
bool SetBlock(bool isblock);
XTcp Accept();
void Close();
int Recv(char *buf,int bufsize);
int Send(const char *buf,int sendsize);

XTcp();
virtual ~XTcp();
int sock = 0;
unsigned short port = 0;
std::string ip;
};

