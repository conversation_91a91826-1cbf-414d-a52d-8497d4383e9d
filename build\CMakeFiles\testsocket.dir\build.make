# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.27

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/xhttp

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/xhttp/build

# Include any dependencies generated for this target.
include CMakeFiles/testsocket.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/testsocket.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/testsocket.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/testsocket.dir/flags.make

CMakeFiles/testsocket.dir/testsocket.cpp.o: CMakeFiles/testsocket.dir/flags.make
CMakeFiles/testsocket.dir/testsocket.cpp.o: /home/<USER>/xhttp/testsocket.cpp
CMakeFiles/testsocket.dir/testsocket.cpp.o: CMakeFiles/testsocket.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/xhttp/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/testsocket.dir/testsocket.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/testsocket.dir/testsocket.cpp.o -MF CMakeFiles/testsocket.dir/testsocket.cpp.o.d -o CMakeFiles/testsocket.dir/testsocket.cpp.o -c /home/<USER>/xhttp/testsocket.cpp

CMakeFiles/testsocket.dir/testsocket.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/testsocket.dir/testsocket.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/xhttp/testsocket.cpp > CMakeFiles/testsocket.dir/testsocket.cpp.i

CMakeFiles/testsocket.dir/testsocket.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/testsocket.dir/testsocket.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/xhttp/testsocket.cpp -o CMakeFiles/testsocket.dir/testsocket.cpp.s

# Object files for target testsocket
testsocket_OBJECTS = \
"CMakeFiles/testsocket.dir/testsocket.cpp.o"

# External object files for target testsocket
testsocket_EXTERNAL_OBJECTS =

bin/testsocket: CMakeFiles/testsocket.dir/testsocket.cpp.o
bin/testsocket: CMakeFiles/testsocket.dir/build.make
bin/testsocket: lib/libXTcp.so
bin/testsocket: CMakeFiles/testsocket.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=/home/<USER>/xhttp/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Linking CXX executable bin/testsocket"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/testsocket.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/testsocket.dir/build: bin/testsocket
.PHONY : CMakeFiles/testsocket.dir/build

CMakeFiles/testsocket.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/testsocket.dir/cmake_clean.cmake
.PHONY : CMakeFiles/testsocket.dir/clean

CMakeFiles/testsocket.dir/depend:
	cd /home/<USER>/xhttp/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/xhttp /home/<USER>/xhttp /home/<USER>/xhttp/build /home/<USER>/xhttp/build /home/<USER>/xhttp/build/CMakeFiles/testsocket.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/testsocket.dir/depend

