# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.27

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/xhttp

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/xhttp/build

# Include any dependencies generated for this target.
include CMakeFiles/XTcp.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/XTcp.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/XTcp.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/XTcp.dir/flags.make

CMakeFiles/XTcp.dir/XTcp.cpp.o: CMakeFiles/XTcp.dir/flags.make
CMakeFiles/XTcp.dir/XTcp.cpp.o: /home/<USER>/xhttp/XTcp.cpp
CMakeFiles/XTcp.dir/XTcp.cpp.o: CMakeFiles/XTcp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/xhttp/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/XTcp.dir/XTcp.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/XTcp.dir/XTcp.cpp.o -MF CMakeFiles/XTcp.dir/XTcp.cpp.o.d -o CMakeFiles/XTcp.dir/XTcp.cpp.o -c /home/<USER>/xhttp/XTcp.cpp

CMakeFiles/XTcp.dir/XTcp.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/XTcp.dir/XTcp.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/xhttp/XTcp.cpp > CMakeFiles/XTcp.dir/XTcp.cpp.i

CMakeFiles/XTcp.dir/XTcp.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/XTcp.dir/XTcp.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/xhttp/XTcp.cpp -o CMakeFiles/XTcp.dir/XTcp.cpp.s

# Object files for target XTcp
XTcp_OBJECTS = \
"CMakeFiles/XTcp.dir/XTcp.cpp.o"

# External object files for target XTcp
XTcp_EXTERNAL_OBJECTS =

lib/libXTcp.so: CMakeFiles/XTcp.dir/XTcp.cpp.o
lib/libXTcp.so: CMakeFiles/XTcp.dir/build.make
lib/libXTcp.so: CMakeFiles/XTcp.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=/home/<USER>/xhttp/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Linking CXX shared library lib/libXTcp.so"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/XTcp.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/XTcp.dir/build: lib/libXTcp.so
.PHONY : CMakeFiles/XTcp.dir/build

CMakeFiles/XTcp.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/XTcp.dir/cmake_clean.cmake
.PHONY : CMakeFiles/XTcp.dir/clean

CMakeFiles/XTcp.dir/depend:
	cd /home/<USER>/xhttp/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/xhttp /home/<USER>/xhttp /home/<USER>/xhttp/build /home/<USER>/xhttp/build /home/<USER>/xhttp/build/CMakeFiles/XTcp.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/XTcp.dir/depend

