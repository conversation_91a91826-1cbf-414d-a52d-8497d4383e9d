<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>C++ 编程技巧与最佳实践</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            line-height: 1.6;
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            max-width: 1200px;
            margin: 0 auto;
            overflow: hidden;
        }

        .header {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            padding: 40px 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 3rem;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        .header p {
            font-size: 1.3rem;
            opacity: 0.9;
        }

        .nav {
            background: #34495e;
            padding: 15px 30px;
        }

        .nav a {
            color: white;
            text-decoration: none;
            margin-right: 20px;
            padding: 8px 16px;
            border-radius: 5px;
            transition: background 0.3s ease;
        }

        .nav a:hover {
            background: #2c3e50;
        }

        .content {
            padding: 40px;
        }

        .section {
            margin-bottom: 40px;
            padding: 30px;
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
        }

        .section h2 {
            color: #667eea;
            margin-bottom: 20px;
            font-size: 2rem;
            border-bottom: 3px solid #667eea;
            padding-bottom: 10px;
        }

        .section h3 {
            color: #2c3e50;
            margin: 25px 0 15px 0;
            font-size: 1.4rem;
        }

        .code-block {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            overflow-x: auto;
            font-family: 'Courier New', monospace;
            position: relative;
        }

        .code-block::before {
            content: 'C++';
            position: absolute;
            top: 10px;
            right: 15px;
            background: #667eea;
            color: white;
            padding: 2px 8px;
            border-radius: 3px;
            font-size: 0.8rem;
        }

        .tip-box {
            background: linear-gradient(120deg, #a8edea 0%, #fed6e3 100%);
            padding: 20px;
            border-radius: 15px;
            margin: 20px 0;
            border-left: 5px solid #667eea;
        }

        .warning-box {
            background: linear-gradient(120deg, #ffeaa7 0%, #fab1a0 100%);
            padding: 20px;
            border-radius: 15px;
            margin: 20px 0;
            border-left: 5px solid #e17055;
        }

        .btn {
            display: inline-block;
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            padding: 12px 30px;
            border-radius: 25px;
            text-decoration: none;
            font-weight: bold;
            margin: 10px 10px 10px 0;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(102, 126, 234, 0.4);
        }

        .footer {
            text-align: center;
            padding: 30px;
            background: #2c3e50;
            color: white;
        }

        @media (max-width: 768px) {
            .header h1 {
                font-size: 2.2rem;
            }
            .content {
                padding: 20px;
            }
            .section {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>C++ 编程技巧</h1>
            <p>现代C++最佳实践与高效编程技巧</p>
        </div>

        <div class="nav">
            <a href="/">← 返回首页</a>
            <a href="/qt-development.html">Qt开发</a>
            <a href="#modern">现代C++</a>
            <a href="#performance">性能优化</a>
            <a href="#patterns">设计模式</a>
        </div>

        <div class="content">
            <div class="section" id="modern">
                <h2>现代C++特性 (C++11/14/17/20)</h2>
                
                <h3>1. 智能指针 - 自动内存管理</h3>
                <div class="code-block">
#include &lt;memory&gt;
#include &lt;iostream&gt;

class Resource {
public:
    Resource(int id) : id_(id) {
        std::cout &lt;&lt; "Resource " &lt;&lt; id_ &lt;&lt; " created\n";
    }
    ~Resource() {
        std::cout &lt;&lt; "Resource " &lt;&lt; id_ &lt;&lt; " destroyed\n";
    }
    void use() { std::cout &lt;&lt; "Using resource " &lt;&lt; id_ &lt;&lt; "\n"; }
private:
    int id_;
};

int main() {
    // unique_ptr - 独占所有权
    auto resource1 = std::make_unique&lt;Resource&gt;(1);
    resource1-&gt;use();
    
    // shared_ptr - 共享所有权
    auto resource2 = std::make_shared&lt;Resource&gt;(2);
    {
        auto resource2_copy = resource2;  // 引用计数 +1
        resource2_copy-&gt;use();
    }  // 引用计数 -1
    
    return 0;  // 自动释放资源
}
                </div>

                <div class="tip-box">
                    <p><strong>💡 最佳实践：</strong> 优先使用 make_unique 和 make_shared，避免直接使用 new/delete。</p>
                </div>

                <h3>2. 自动类型推导与范围for循环</h3>
                <div class="code-block">
#include &lt;vector&gt;
#include &lt;map&gt;
#include &lt;string&gt;

int main() {
    // auto 关键字
    auto numbers = std::vector&lt;int&gt;{1, 2, 3, 4, 5};
    auto name = std::string("C++");
    
    // 范围for循环
    for (const auto&amp; num : numbers) {
        std::cout &lt;&lt; num &lt;&lt; " ";
    }
    
    // 结构化绑定 (C++17)
    auto person_info = std::map&lt;std::string, int&gt;{
        {"Alice", 25}, {"Bob", 30}
    };
    
    for (const auto&amp; [name, age] : person_info) {
        std::cout &lt;&lt; name &lt;&lt; " is " &lt;&lt; age &lt;&lt; " years old\n";
    }
    
    return 0;
}
                </div>

                <h3>3. Lambda 表达式</h3>
                <div class="code-block">
#include &lt;algorithm&gt;
#include &lt;vector&gt;
#include &lt;functional&gt;

int main() {
    std::vector&lt;int&gt; numbers = {5, 2, 8, 1, 9};
    
    // 简单lambda
    std::sort(numbers.begin(), numbers.end(), 
              [](int a, int b) { return a &lt; b; });
    
    // 捕获外部变量
    int threshold = 5;
    auto count = std::count_if(numbers.begin(), numbers.end(),
                              [threshold](int n) { return n &gt; threshold; });
    
    // 泛型lambda (C++14)
    auto generic_lambda = [](auto a, auto b) { return a + b; };
    
    return 0;
}
                </div>
            </div>

            <div class="section" id="performance">
                <h2>性能优化技巧</h2>
                
                <h3>1. 移动语义 - 避免不必要的拷贝</h3>
                <div class="code-block">
#include &lt;vector&gt;
#include &lt;string&gt;
#include &lt;utility&gt;

class DataContainer {
private:
    std::vector&lt;std::string&gt; data_;
    
public:
    // 移动构造函数
    DataContainer(DataContainer&amp;&amp; other) noexcept 
        : data_(std::move(other.data_)) {}
    
    // 移动赋值操作符
    DataContainer&amp; operator=(DataContainer&amp;&amp; other) noexcept {
        if (this != &amp;other) {
            data_ = std::move(other.data_);
        }
        return *this;
    }
    
    // 完美转发
    template&lt;typename T&gt;
    void add_data(T&amp;&amp; item) {
        data_.emplace_back(std::forward&lt;T&gt;(item));
    }
};
                </div>

                <h3>2. 内存池与对象池</h3>
                <div class="code-block">
#include &lt;memory&gt;
#include &lt;vector&gt;

template&lt;typename T, size_t BlockSize = 4096&gt;
class MemoryPool {
private:
    struct Block {
        alignas(T) char data[BlockSize];
        Block* next;
    };
    
    Block* current_block_;
    char* current_pos_;
    char* end_pos_;
    
public:
    template&lt;typename... Args&gt;
    T* allocate(Args&amp;&amp;... args) {
        if (current_pos_ + sizeof(T) &gt; end_pos_) {
            allocate_new_block();
        }
        
        T* result = reinterpret_cast&lt;T*&gt;(current_pos_);
        current_pos_ += sizeof(T);
        
        new(result) T(std::forward&lt;Args&gt;(args)...);
        return result;
    }
    
private:
    void allocate_new_block() {
        Block* new_block = new Block;
        new_block-&gt;next = current_block_;
        current_block_ = new_block;
        current_pos_ = current_block_-&gt;data;
        end_pos_ = current_block_-&gt;data + BlockSize;
    }
};
                </div>

                <div class="warning-box">
                    <p><strong>⚠️ 注意：</strong> 过早优化是万恶之源。先确保代码正确性，再进行性能优化。</p>
                </div>
            </div>

            <div class="section" id="patterns">
                <h2>常用设计模式</h2>
                
                <h3>1. RAII (资源获取即初始化)</h3>
                <div class="code-block">
#include &lt;mutex&gt;
#include &lt;fstream&gt;

class FileManager {
private:
    std::ofstream file_;
    std::mutex mutex_;
    
public:
    FileManager(const std::string&amp; filename) : file_(filename) {
        if (!file_.is_open()) {
            throw std::runtime_error("Failed to open file");
        }
    }
    
    ~FileManager() {
        if (file_.is_open()) {
            file_.close();
        }
    }
    
    void write_line(const std::string&amp; line) {
        std::lock_guard&lt;std::mutex&gt; lock(mutex_);  // RAII锁
        file_ &lt;&lt; line &lt;&lt; std::endl;
    }
};
                </div>

                <h3>2. 单例模式 (线程安全)</h3>
                <div class="code-block">
#include &lt;mutex&gt;

class Singleton {
private:
    static std::once_flag initialized_;
    static std::unique_ptr&lt;Singleton&gt; instance_;
    
    Singleton() = default;
    
public:
    static Singleton&amp; get_instance() {
        std::call_once(initialized_, []() {
            instance_ = std::make_unique&lt;Singleton&gt;();
        });
        return *instance_;
    }
    
    // 禁止拷贝和赋值
    Singleton(const Singleton&amp;) = delete;
    Singleton&amp; operator=(const Singleton&amp;) = delete;
};

std::once_flag Singleton::initialized_;
std::unique_ptr&lt;Singleton&gt; Singleton::instance_;
                </div>

                <div class="tip-box">
                    <p><strong>💡 现代C++技巧：</strong> 使用 std::call_once 实现线程安全的单例模式，比传统的双重检查锁定更简洁。</p>
                </div>
            </div>

            <div class="section">
                <h2>编程建议</h2>
                
                <h3>代码质量原则</h3>
                <ul style="margin-left: 20px; margin-bottom: 20px;">
                    <li style="margin-bottom: 10px;">🎯 <strong>单一职责原则</strong> - 每个类只负责一个功能</li>
                    <li style="margin-bottom: 10px;">🔒 <strong>封装性</strong> - 隐藏实现细节，提供清晰接口</li>
                    <li style="margin-bottom: 10px;">🔄 <strong>DRY原则</strong> - Don't Repeat Yourself</li>
                    <li style="margin-bottom: 10px;">📝 <strong>代码可读性</strong> - 代码是写给人看的</li>
                    <li>🧪 <strong>测试驱动开发</strong> - 先写测试，再写实现</li>
                </ul>

                <a href="/qt-development.html" class="btn">学习Qt开发</a>
                <a href="/" class="btn">返回首页</a>
            </div>
        </div>

        <div class="footer">
            <p>&copy; 2025 C++编程技巧指南 | 让代码更优雅高效</p>
        </div>
    </div>
</body>
</html>
