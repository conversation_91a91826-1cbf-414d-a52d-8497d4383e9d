<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>C++ Qt 开发指南</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #2c3e50 0%, #3498db 100%);
            color: #333;
            line-height: 1.6;
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            max-width: 1200px;
            margin: 0 auto;
            overflow: hidden;
        }

        .header {
            background: linear-gradient(45deg, #e74c3c, #c0392b);
            color: white;
            padding: 40px 30px;
            text-align: center;
            position: relative;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="40" r="1.5" fill="rgba(255,255,255,0.1)"/><circle cx="40" cy="80" r="1" fill="rgba(255,255,255,0.1)"/></svg>');
        }

        .header h1 {
            font-size: 3rem;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
            position: relative;
            z-index: 1;
        }

        .header p {
            font-size: 1.3rem;
            opacity: 0.9;
            position: relative;
            z-index: 1;
        }

        .nav {
            background: #34495e;
            padding: 15px 30px;
        }

        .nav a {
            color: white;
            text-decoration: none;
            margin-right: 20px;
            padding: 8px 16px;
            border-radius: 5px;
            transition: background 0.3s ease;
        }

        .nav a:hover {
            background: #2c3e50;
        }

        .content {
            padding: 40px;
        }

        .section {
            margin-bottom: 40px;
            padding: 30px;
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
            transition: transform 0.3s ease;
        }

        .section:hover {
            transform: translateY(-5px);
        }

        .section h2 {
            color: #e74c3c;
            margin-bottom: 20px;
            font-size: 2rem;
            border-bottom: 3px solid #e74c3c;
            padding-bottom: 10px;
        }

        .section h3 {
            color: #2c3e50;
            margin: 25px 0 15px 0;
            font-size: 1.4rem;
        }

        .section p {
            font-size: 1.1rem;
            color: #555;
            margin-bottom: 15px;
        }

        .code-block {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            overflow-x: auto;
            font-family: 'Courier New', monospace;
            position: relative;
        }

        .code-block::before {
            content: 'C++';
            position: absolute;
            top: 10px;
            right: 15px;
            background: #e74c3c;
            color: white;
            padding: 2px 8px;
            border-radius: 3px;
            font-size: 0.8rem;
        }

        .highlight {
            background: linear-gradient(120deg, #74b9ff 0%, #0984e3 100%);
            color: white;
            padding: 25px;
            border-radius: 15px;
            margin: 25px 0;
            border-left: 5px solid #2d3436;
        }

        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }

        .feature-card {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 10px;
            border-left: 4px solid #e74c3c;
            transition: all 0.3s ease;
        }

        .feature-card:hover {
            background: #e9ecef;
            transform: translateX(5px);
        }

        .feature-card h4 {
            color: #2c3e50;
            margin-bottom: 10px;
            font-size: 1.2rem;
        }

        .btn {
            display: inline-block;
            background: linear-gradient(45deg, #e74c3c, #c0392b);
            color: white;
            padding: 12px 30px;
            border-radius: 25px;
            text-decoration: none;
            font-weight: bold;
            margin: 10px 10px 10px 0;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
            box-shadow: 0 4px 15px rgba(231, 76, 60, 0.3);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(231, 76, 60, 0.4);
        }

        .footer {
            text-align: center;
            padding: 30px;
            background: #2c3e50;
            color: white;
        }

        @media (max-width: 768px) {
            .header h1 {
                font-size: 2.2rem;
            }
            .content {
                padding: 20px;
            }
            .section {
                padding: 20px;
            }
            .feature-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>C++ Qt 开发指南</h1>
            <p>跨平台应用程序开发的强大框架</p>
        </div>

        <div class="nav">
            <a href="/">← 返回首页</a>
            <a href="#intro">Qt简介</a>
            <a href="#features">核心特性</a>
            <a href="#examples">代码示例</a>
            <a href="#resources">学习资源</a>
        </div>

        <div class="content">
            <div class="section" id="intro">
                <h2>Qt 框架简介</h2>
                <p>Qt 是一个跨平台的 C++ 应用程序开发框架，广泛用于开发GUI程序，也可用于开发非GUI程序。Qt 使用标准的 C++，但大量使用特殊的代码生成扩展（称为元对象编译器 Meta Object Compiler，moc）以及一些宏。</p>
                
                <div class="highlight">
                    <p><strong>为什么选择 Qt？</strong></p>
                    <p>Qt 提供了丰富的API和工具，让开发者能够快速构建现代化的桌面、移动和嵌入式应用程序。</p>
                </div>

                <h3>Qt 的主要优势</h3>
                <div class="feature-grid">
                    <div class="feature-card">
                        <h4>🌍 跨平台支持</h4>
                        <p>一次编写，到处运行。支持Windows、macOS、Linux、Android、iOS等多个平台。</p>
                    </div>
                    <div class="feature-card">
                        <h4>🎨 丰富的UI组件</h4>
                        <p>提供大量预制的UI控件和自定义样式系统，轻松创建美观的用户界面。</p>
                    </div>
                    <div class="feature-card">
                        <h4>⚡ 高性能</h4>
                        <p>基于C++的高性能框架，提供硬件加速的图形渲染和优化的内存管理。</p>
                    </div>
                    <div class="feature-card">
                        <h4>🔧 完整的开发工具链</h4>
                        <p>Qt Creator IDE、Qt Designer界面设计器、调试工具等完整的开发环境。</p>
                    </div>
                </div>
            </div>

            <div class="section" id="features">
                <h2>核心特性与模块</h2>
                
                <h3>Qt Core 核心模块</h3>
                <p>提供核心的非GUI功能，包括事件循环、对象系统、信号槽机制等。</p>

                <h3>Qt Widgets 界面模块</h3>
                <p>传统的桌面风格UI组件，如按钮、文本框、表格等。</p>

                <h3>Qt Quick/QML</h3>
                <p>现代化的声明式UI框架，支持流畅的动画和触摸交互。</p>

                <div class="code-block">
// Qt Quick QML 示例
import QtQuick 2.15
import QtQuick.Controls 2.15

ApplicationWindow {
    visible: true
    width: 640
    height: 480
    title: "Qt Quick 应用"
    
    Rectangle {
        anchors.fill: parent
        gradient: Gradient {
            GradientStop { position: 0.0; color: "#74b9ff" }
            GradientStop { position: 1.0; color: "#0984e3" }
        }
        
        Text {
            anchors.centerIn: parent
            text: "欢迎使用 Qt Quick!"
            font.pixelSize: 24
            color: "white"
        }
    }
}
                </div>
            </div>

            <div class="section" id="examples">
                <h2>代码示例</h2>
                
                <h3>1. 简单的 Hello World 应用</h3>
                <div class="code-block">
#include &lt;QApplication&gt;
#include &lt;QLabel&gt;
#include &lt;QVBoxLayout&gt;
#include &lt;QWidget&gt;
#include &lt;QPushButton&gt;

int main(int argc, char *argv[])
{
    QApplication app(argc, argv);
    
    QWidget window;
    window.setWindowTitle("Qt Hello World");
    window.resize(300, 200);
    
    QVBoxLayout *layout = new QVBoxLayout;
    
    QLabel *label = new QLabel("欢迎使用 Qt!");
    label-&gt;setAlignment(Qt::AlignCenter);
    
    QPushButton *button = new QPushButton("点击我");
    
    layout-&gt;addWidget(label);
    layout-&gt;addWidget(button);
    
    window.setLayout(layout);
    window.show();
    
    return app.exec();
}
                </div>

                <h3>2. 信号槽机制示例</h3>
                <div class="code-block">
#include &lt;QApplication&gt;
#include &lt;QPushButton&gt;
#include &lt;QMessageBox&gt;

int main(int argc, char *argv[])
{
    QApplication app(argc, argv);
    
    QPushButton button("点击显示消息");
    button.resize(200, 50);
    
    // 连接信号和槽
    QObject::connect(&amp;button, &amp;QPushButton::clicked, [](){
        QMessageBox::information(nullptr, "消息", "按钮被点击了!");
    });
    
    button.show();
    
    return app.exec();
}
                </div>

                <h3>3. 自定义窗口类</h3>
                <div class="code-block">
// mainwindow.h
#ifndef MAINWINDOW_H
#define MAINWINDOW_H

#include &lt;QMainWindow&gt;
#include &lt;QPushButton&gt;
#include &lt;QLabel&gt;

class MainWindow : public QMainWindow
{
    Q_OBJECT

public:
    MainWindow(QWidget *parent = nullptr);

private slots:
    void onButtonClicked();

private:
    QPushButton *m_button;
    QLabel *m_label;
    int m_clickCount;
};

#endif // MAINWINDOW_H
                </div>
            </div>

            <div class="section" id="resources">
                <h2>学习资源</h2>
                
                <h3>官方资源</h3>
                <p>Qt 官方提供了丰富的学习材料和文档：</p>
                <ul style="margin-left: 20px; margin-bottom: 20px;">
                    <li style="margin-bottom: 10px;">📚 <strong>Qt Documentation</strong> - 完整的API文档和教程</li>
                    <li style="margin-bottom: 10px;">🎯 <strong>Qt Examples</strong> - 官方示例代码集合</li>
                    <li style="margin-bottom: 10px;">🎥 <strong>Qt Webinars</strong> - 在线技术讲座</li>
                    <li style="margin-bottom: 10px;">💬 <strong>Qt Forum</strong> - 开发者社区论坛</li>
                </ul>

                <h3>推荐书籍</h3>
                <div class="feature-grid">
                    <div class="feature-card">
                        <h4>《C++ Qt设计模式》</h4>
                        <p>深入理解Qt框架的设计理念和最佳实践。</p>
                    </div>
                    <div class="feature-card">
                        <h4>《Qt Creator快速入门》</h4>
                        <p>掌握Qt Creator IDE的使用技巧和开发流程。</p>
                    </div>
                </div>

                <div class="highlight">
                    <p><strong>开始你的Qt之旅</strong></p>
                    <p>建议从Qt Widgets开始学习，掌握基本概念后再学习Qt Quick/QML。实践是最好的老师！</p>
                </div>

                <a href="#" class="btn">下载Qt开发环境</a>
                <a href="#" class="btn">查看更多示例</a>
            </div>
        </div>

        <div class="footer">
            <p>&copy; 2025 Qt开发指南 | 让跨平台开发更简单</p>
            <p>Qt是The Qt Company的注册商标</p>
        </div>
    </div>
</body>
</html>
