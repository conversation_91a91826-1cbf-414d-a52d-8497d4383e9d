{"artifacts": [{"path": "bin/testsocket"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_executable", "install", "target_link_libraries", "set_property", "find_package", "include_directories"], "files": ["CMakeLists.txt", "/usr/share/cmake-3.27/Modules/FindThreads.cmake"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 47, "parent": 0}, {"command": 1, "file": 0, "line": 70, "parent": 0}, {"command": 2, "file": 0, "line": 52, "parent": 0}, {"command": 4, "file": 0, "line": 26, "parent": 0}, {"file": 1, "parent": 4}, {"command": 3, "file": 1, "line": 238, "parent": 5}, {"command": 5, "file": 0, "line": 29, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": " -Wall -Wextra -std=gnu++11"}], "includes": [{"backtrace": 7, "path": "/home/<USER>/xhttp/Http_Server"}], "language": "CXX", "languageStandard": {"backtraces": [1], "standard": "11"}, "sourceIndexes": [0]}], "dependencies": [{"backtrace": 3, "id": "XTcp::@6890427a1f51a3e7e1df"}], "id": "testsocket::@6890427a1f51a3e7e1df", "install": {"destinations": [{"backtrace": 2, "path": "bin"}], "prefix": {"path": "/usr/local"}}, "link": {"commandFragments": [{"fragment": "-Wall -Wextra", "role": "flags"}, {"fragment": "", "role": "flags"}, {"fragment": "-Wl,-rpath,/home/<USER>/xhttp/build/lib:", "role": "libraries"}, {"backtrace": 3, "fragment": "lib/libXTcp.so", "role": "libraries"}, {"backtrace": 6, "fragment": "-l<PERSON><PERSON><PERSON>", "role": "libraries"}], "language": "CXX"}, "name": "testsocket", "nameOnDisk": "testsocket", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "testsocket.cpp", "sourceGroupIndex": 0}], "type": "EXECUTABLE"}