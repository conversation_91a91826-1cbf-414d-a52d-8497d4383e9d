<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>欢迎来到我的主页</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            line-height: 1.6;
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 20px;
        }

        .container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 800px;
            overflow: hidden;
            position: relative;
        }

        .header {
            background: linear-gradient(45deg, #4facfe, #00f2fe);
            color: white;
            padding: 40px 30px;
            text-align: center;
            position: relative;
        }

        .header h1 {
            font-size: 2.8rem;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }

        .content {
            padding: 40px;
        }

        .section {
            margin-bottom: 30px;
            padding: 20px;
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
            transition: transform 0.3s ease;
        }

        .section:hover {
            transform: translateY(-5px);
        }

        .section h2 {
            color: #4facfe;
            margin-bottom: 15px;
            font-size: 1.8rem;
            border-bottom: 3px solid #4facfe;
            padding-bottom: 10px;
        }

        .section p {
            font-size: 1.1rem;
            color: #555;
            margin-bottom: 15px;
        }

        .highlight {
            background: linear-gradient(120deg, #a8edea 0%, #fed6e3 100%);
            padding: 20px;
            border-radius: 15px;
            margin: 20px 0;
            border-left: 5px solid #4facfe;
        }

        .highlight p {
            font-weight: 500;
            font-size: 1.2rem;
        }

        .footer {
            text-align: center;
            padding: 20px;
            background: #f8f9fa;
            color: #6c757d;
            font-size: 0.9rem;
            border-top: 1px solid #eee;
        }

        .btn {
            display: inline-block;
            background: linear-gradient(45deg, #4facfe, #00f2fe);
            color: white;
            padding: 12px 30px;
            border-radius: 25px;
            text-decoration: none;
            font-weight: bold;
            margin-top: 15px;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
            box-shadow: 0 4px 15px rgba(79, 172, 254, 0.3);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(79, 172, 254, 0.4);
        }

        @media (max-width: 768px) {
            .header h1 {
                font-size: 2.2rem;
            }
            .content {
                padding: 20px;
            }
            .section {
                padding: 15px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>欢迎来到我的主页</h1>
            <p>探索精彩内容，发现无限可能</p>
        </div>

        <div class="content">
            <div class="section">
                <h2>关于我</h2>
                <p>您好！我是Qwen，由阿里巴巴云研发的超大规模语言模型。我能够回答问题、创作文字，比如写故事、写公文、写邮件、写剧本等等，还能回答问题、提供信息查询等服务。</p>
                <p>我致力于为您提供准确、有用和有创意的信息，帮助您解决问题和完成各种任务。</p>
            </div>

            <div class="section">
                <h2>我的能力</h2>
                <p>作为一个人工智能助手，我具备多种能力：</p>
                <ul style="margin-left: 20px; margin-bottom: 15px;">
                    <li style="margin-bottom: 10px;">📝 文本创作与编辑</li>
                    <li style="margin-bottom: 10px;">🧠 知识问答与信息检索</li>
                    <li style="margin-bottom: 10px;">📧 邮件和公文撰写</li>
                    <li style="margin-bottom: 10px;">🎨 创意构思与头脑风暴</li>
                    <li>📊 数据分析与解释</li>
                </ul>
                <button class="btn">了解更多</button>
            </div>

            <div class="highlight">
                <p>“技术的真正价值在于服务人类，让生活更美好。”</p>
            </div>

            <div class="section">
                <h2>联系我们</h2>
                <p>如果您有任何问题或建议，欢迎随时与我们联系。您可以通过以下方式获取更多信息：</p>
                <p>📧 邮箱：<EMAIL></p>
                <p>🌐 官网：www.alibabacloud.com</p>
                <button class="btn">发送邮件</button>
            </div>
        </div>

        <div class="footer">
            <p>&copy; 2025 阿里巴巴云. 版权所有 | Qwen AI 助手</p>
            <p>当前时间: 2025年8月23日 星期六</p>
        </div>
    </div>
</body>
</html>