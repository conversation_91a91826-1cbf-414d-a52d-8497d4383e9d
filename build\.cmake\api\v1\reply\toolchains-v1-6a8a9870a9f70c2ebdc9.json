{"kind": "toolchains", "toolchains": [{"compiler": {"id": "GNU", "implicit": {"includeDirectories": ["/home/<USER>/software/vcpkg/vcpkg/installed/x64-linux/include", "/usr/lib/gcc/x86_64-linux-gnu/9/include", "/usr/local/include", "/usr/include/x86_64-linux-gnu", "/usr/include"], "linkDirectories": ["/home/<USER>/software/vcpkg/vcpkg/installed/x64-linux/lib", "/usr/lib/gcc/x86_64-linux-gnu/9", "/usr/lib/x86_64-linux-gnu", "/usr/lib", "/lib/x86_64-linux-gnu", "/lib"], "linkFrameworkDirectories": [], "linkLibraries": ["gcc", "gcc_s", "c", "gcc", "gcc_s"]}, "path": "/usr/bin/cc", "version": "9.4.0"}, "language": "C", "sourceFileExtensions": ["c", "m"]}, {"compiler": {"id": "GNU", "implicit": {"includeDirectories": ["/home/<USER>/software/vcpkg/vcpkg/installed/x64-linux/include", "/usr/include/c++/9", "/usr/include/x86_64-linux-gnu/c++/9", "/usr/include/c++/9/backward", "/usr/lib/gcc/x86_64-linux-gnu/9/include", "/usr/local/include", "/usr/include/x86_64-linux-gnu", "/usr/include"], "linkDirectories": ["/home/<USER>/software/vcpkg/vcpkg/installed/x64-linux/lib", "/usr/lib/gcc/x86_64-linux-gnu/9", "/usr/lib/x86_64-linux-gnu", "/usr/lib", "/lib/x86_64-linux-gnu", "/lib"], "linkFrameworkDirectories": [], "linkLibraries": ["stdc++", "m", "gcc_s", "gcc", "c", "gcc_s", "gcc"]}, "path": "/usr/bin/c++", "version": "9.4.0"}, "language": "CXX", "sourceFileExtensions": ["C", "M", "c++", "cc", "cpp", "cxx", "mm", "mpp", "CPP", "ixx", "cppm", "ccm", "cxxm", "c++m"]}], "version": {"major": 1, "minor": 0}}