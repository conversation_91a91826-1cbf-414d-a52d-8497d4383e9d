{"artifacts": [{"path": "lib/libXTcp.so"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_library", "install", "set_property", "find_package", "target_compile_definitions", "include_directories"], "files": ["CMakeLists.txt", "/usr/share/cmake-3.27/Modules/FindThreads.cmake"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 32, "parent": 0}, {"command": 1, "file": 0, "line": 70, "parent": 0}, {"command": 3, "file": 0, "line": 26, "parent": 0}, {"file": 1, "parent": 3}, {"command": 2, "file": 1, "line": 238, "parent": 4}, {"command": 4, "file": 0, "line": 38, "parent": 0}, {"command": 5, "file": 0, "line": 29, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": " -Wall -Wextra -std=gnu++11 -fPIC"}], "defines": [{"backtrace": 6, "define": "XTCP_EXPORTS"}, {"define": "XTcp_EXPORTS"}], "includes": [{"backtrace": 7, "path": "/home/<USER>/xhttp/Http_Server"}], "language": "CXX", "languageStandard": {"backtraces": [1], "standard": "11"}, "sourceIndexes": [0]}], "id": "XTcp::@6890427a1f51a3e7e1df", "install": {"destinations": [{"backtrace": 2, "path": "lib"}, {"backtrace": 2, "path": "lib"}], "prefix": {"path": "/usr/local"}}, "link": {"commandFragments": [{"fragment": "", "role": "flags"}, {"backtrace": 5, "fragment": "-l<PERSON><PERSON><PERSON>", "role": "libraries"}], "language": "CXX"}, "name": "XTcp", "nameOnDisk": "libXTcp.so", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0]}, {"name": "<PERSON><PERSON>", "sourceIndexes": [1]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "XTcp.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "XTcp.h", "sourceGroupIndex": 1}], "type": "SHARED_LIBRARY"}