{"configurations": [{"directories": [{"build": ".", "hasInstallRule": true, "jsonFile": "directory-.-cafee9ae64ec3f240284.json", "minimumCMakeVersion": {"string": "3.10"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1]}], "name": "", "projects": [{"directoryIndexes": [0], "name": "Http_Server", "targetIndexes": [0, 1]}], "targets": [{"directoryIndex": 0, "id": "XTcp::@6890427a1f51a3e7e1df", "jsonFile": "target-XTcp-726ac4fe896d6c8b3811.json", "name": "XTcp", "projectIndex": 0}, {"directoryIndex": 0, "id": "testsocket::@6890427a1f51a3e7e1df", "jsonFile": "target-testsocket-9f1ffc89a6fc05658b39.json", "name": "testsocket", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "/home/<USER>/xhttp/build", "source": "/home/<USER>/xhttp"}, "version": {"major": 2, "minor": 6}}