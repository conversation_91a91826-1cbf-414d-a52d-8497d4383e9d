
---
events:
  -
    kind: "message-v1"
    backtrace:
      - "/usr/share/cmake-3.27/Modules/CMakeDetermineSystem.cmake:211 (message)"
      - "CMakeLists.txt:4 (project)"
    message: |
      The system is: Linux - 4.4.0-22621-Microsoft - x86_64
  -
    kind: "message-v1"
    backtrace:
      - "/usr/share/cmake-3.27/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "/usr/share/cmake-3.27/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "/usr/share/cmake-3.27/Modules/CMakeDetermineCCompiler.cmake:123 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:4 (project)"
    message: |
      Compiling the C compiler identification source file "CMakeCCompilerId.c" succeeded.
      Compiler: /usr/bin/cc 
      Build flags: 
      Id flags:  
      
      The output was:
      0
      
      
      Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "a.out"
      
      The C compiler identification is GNU, found in:
        /home/<USER>/xhttp/build/CMakeFiles/3.27.9/CompilerIdC/a.out
      
  -
    kind: "message-v1"
    backtrace:
      - "/usr/share/cmake-3.27/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "/usr/share/cmake-3.27/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "/usr/share/cmake-3.27/Modules/CMakeDetermineCXXCompiler.cmake:126 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:4 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" succeeded.
      Compiler: /usr/bin/c++ 
      Build flags: 
      Id flags:  
      
      The output was:
      0
      
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "a.out"
      
      The CXX compiler identification is GNU, found in:
        /home/<USER>/xhttp/build/CMakeFiles/3.27.9/CompilerIdCXX/a.out
      
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/share/cmake-3.27/Modules/CMakeDetermineCompilerABI.cmake:57 (try_compile)"
      - "/usr/share/cmake-3.27/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:4 (project)"
    checks:
      - "Detecting C compiler ABI info"
    directories:
      source: "/home/<USER>/xhttp/build/CMakeFiles/CMakeScratch/TryCompile-jsbfrE"
      binary: "/home/<USER>/xhttp/build/CMakeFiles/CMakeScratch/TryCompile-jsbfrE"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
    buildResult:
      variable: "CMAKE_C_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: '/home/<USER>/xhttp/build/CMakeFiles/CMakeScratch/TryCompile-jsbfrE'
        
        Run Build Command(s): /usr/bin/cmake -E env VERBOSE=1 /usr/bin/make -f Makefile cmTC_6f10b/fast
        /usr/bin/make  -f CMakeFiles/cmTC_6f10b.dir/build.make CMakeFiles/cmTC_6f10b.dir/build
        make[1]: Entering directory '/home/<USER>/xhttp/build/CMakeFiles/CMakeScratch/TryCompile-jsbfrE'
        Building C object CMakeFiles/cmTC_6f10b.dir/CMakeCCompilerABI.c.o
        /usr/bin/cc   -v -o CMakeFiles/cmTC_6f10b.dir/CMakeCCompilerABI.c.o -c /usr/share/cmake-3.27/Modules/CMakeCCompilerABI.c
        Using built-in specs.
        COLLECT_GCC=/usr/bin/cc
        OFFLOAD_TARGET_NAMES=nvptx-none:hsa
        OFFLOAD_TARGET_DEFAULT=1
        Target: x86_64-linux-gnu
        Configured with: ../src/configure -v --with-pkgversion='Ubuntu 9.4.0-1ubuntu1~20.04.2' --with-bugurl=file:///usr/share/doc/gcc-9/README.Bugs --enable-languages=c,ada,c++,go,brig,d,fortran,objc,obj-c++,gm2 --prefix=/usr --with-gcc-major-version-only --program-suffix=-9 --program-prefix=x86_64-linux-gnu- --enable-shared --enable-linker-build-id --libexecdir=/usr/lib --without-included-gettext --enable-threads=posix --libdir=/usr/lib --enable-nls --enable-clocale=gnu --enable-libstdcxx-debug --enable-libstdcxx-time=yes --with-default-libstdcxx-abi=new --enable-gnu-unique-object --disable-vtable-verify --enable-plugin --enable-default-pie --with-system-zlib --with-target-system-zlib=auto --enable-objc-gc=auto --enable-multiarch --disable-werror --with-arch-32=i686 --with-abi=m64 --with-multilib-list=m32,m64,mx32 --enable-multilib --with-tune=generic --enable-offload-targets=nvptx-none=/build/gcc-9-9QDOt0/gcc-9-9.4.0/debian/tmp-nvptx/usr,hsa --without-cuda-driver --enable-checking=release --build=x86_64-linux-gnu --host=x86_64-linux-gnu --target=x86_64-linux-gnu
        Thread model: posix
        gcc version 9.4.0 (Ubuntu 9.4.0-1ubuntu1~20.04.2) 
        COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles/cmTC_6f10b.dir/CMakeCCompilerABI.c.o' '-c' '-mtune=generic' '-march=x86-64'
         /usr/lib/gcc/x86_64-linux-gnu/9/cc1 -quiet -v -imultiarch x86_64-linux-gnu /usr/share/cmake-3.27/Modules/CMakeCCompilerABI.c -quiet -dumpbase CMakeCCompilerABI.c -mtune=generic -march=x86-64 -auxbase-strip CMakeFiles/cmTC_6f10b.dir/CMakeCCompilerABI.c.o -version -fasynchronous-unwind-tables -fstack-protector-strong -Wformat -Wformat-security -fstack-clash-protection -fcf-protection -o /tmp/ccuag7QK.s
        GNU C17 (Ubuntu 9.4.0-1ubuntu1~20.04.2) version 9.4.0 (x86_64-linux-gnu)
        	compiled by GNU C version 9.4.0, GMP version 6.2.0, MPFR version 4.0.2, MPC version 1.1.0, isl version isl-0.22.1-GMP
        
        GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072
        ignoring nonexistent directory "/usr/local/include/x86_64-linux-gnu"
        ignoring nonexistent directory "/usr/lib/gcc/x86_64-linux-gnu/9/include-fixed"
        ignoring nonexistent directory "/usr/lib/gcc/x86_64-linux-gnu/9/../../../../x86_64-linux-gnu/include"
        ignoring duplicate directory "/home/<USER>/software/vcpkg/vcpkg/installed/x64-linux/include"
        #include "..." search starts here:
        #include <...> search starts here:
         /home/<USER>/software/vcpkg/vcpkg/installed/x64-linux/include
         .
         /usr/lib/gcc/x86_64-linux-gnu/9/include
         /usr/local/include
         /usr/include/x86_64-linux-gnu
         /usr/include
        End of search list.
        GNU C17 (Ubuntu 9.4.0-1ubuntu1~20.04.2) version 9.4.0 (x86_64-linux-gnu)
        	compiled by GNU C version 9.4.0, GMP version 6.2.0, MPFR version 4.0.2, MPC version 1.1.0, isl version isl-0.22.1-GMP
        
        GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072
        Compiler executable checksum: 01da938ff5dc2163489aa33cb3b747a7
        COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles/cmTC_6f10b.dir/CMakeCCompilerABI.c.o' '-c' '-mtune=generic' '-march=x86-64'
         as -v --64 -o CMakeFiles/cmTC_6f10b.dir/CMakeCCompilerABI.c.o /tmp/ccuag7QK.s
        GNU assembler version 2.34 (x86_64-linux-gnu) using BFD version (GNU Binutils for Ubuntu) 2.34
        COMPILER_PATH=/usr/lib/gcc/x86_64-linux-gnu/9/:/usr/lib/gcc/x86_64-linux-gnu/9/:/usr/lib/gcc/x86_64-linux-gnu/:/usr/lib/gcc/x86_64-linux-gnu/9/:/usr/lib/gcc/x86_64-linux-gnu/
        LIBRARY_PATH=/home/<USER>/software/vcpkg/vcpkg/installed/x64-linux/lib/../lib/:/home/<USER>/software/vcpkg/vcpkg/installed/x64-linux/lib/../lib/:/usr/lib/gcc/x86_64-linux-gnu/9/:/usr/lib/gcc/x86_64-linux-gnu/9/../../../x86_64-linux-gnu/:/usr/lib/gcc/x86_64-linux-gnu/9/../../../../lib/:/lib/x86_64-linux-gnu/:/lib/../lib/:/usr/lib/x86_64-linux-gnu/:/usr/lib/../lib/:/home/<USER>/software/vcpkg/vcpkg/installed/x64-linux/lib/:/home/<USER>/software/vcpkg/vcpkg/installed/x64-linux/lib/:./:/usr/lib/gcc/x86_64-linux-gnu/9/../../../:/lib/:/usr/lib/
        COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles/cmTC_6f10b.dir/CMakeCCompilerABI.c.o' '-c' '-mtune=generic' '-march=x86-64'
        Linking C executable cmTC_6f10b
        /usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_6f10b.dir/link.txt --verbose=1
        /usr/bin/cc  -v CMakeFiles/cmTC_6f10b.dir/CMakeCCompilerABI.c.o -o cmTC_6f10b 
        Using built-in specs.
        COLLECT_GCC=/usr/bin/cc
        COLLECT_LTO_WRAPPER=/usr/lib/gcc/x86_64-linux-gnu/9/lto-wrapper
        OFFLOAD_TARGET_NAMES=nvptx-none:hsa
        OFFLOAD_TARGET_DEFAULT=1
        Target: x86_64-linux-gnu
        Configured with: ../src/configure -v --with-pkgversion='Ubuntu 9.4.0-1ubuntu1~20.04.2' --with-bugurl=file:///usr/share/doc/gcc-9/README.Bugs --enable-languages=c,ada,c++,go,brig,d,fortran,objc,obj-c++,gm2 --prefix=/usr --with-gcc-major-version-only --program-suffix=-9 --program-prefix=x86_64-linux-gnu- --enable-shared --enable-linker-build-id --libexecdir=/usr/lib --without-included-gettext --enable-threads=posix --libdir=/usr/lib --enable-nls --enable-clocale=gnu --enable-libstdcxx-debug --enable-libstdcxx-time=yes --with-default-libstdcxx-abi=new --enable-gnu-unique-object --disable-vtable-verify --enable-plugin --enable-default-pie --with-system-zlib --with-target-system-zlib=auto --enable-objc-gc=auto --enable-multiarch --disable-werror --with-arch-32=i686 --with-abi=m64 --with-multilib-list=m32,m64,mx32 --enable-multilib --with-tune=generic --enable-offload-targets=nvptx-none=/build/gcc-9-9QDOt0/gcc-9-9.4.0/debian/tmp-nvptx/usr,hsa --without-cuda-driver --enable-checking=release --build=x86_64-linux-gnu --host=x86_64-linux-gnu --target=x86_64-linux-gnu
        Thread model: posix
        gcc version 9.4.0 (Ubuntu 9.4.0-1ubuntu1~20.04.2) 
        COMPILER_PATH=/usr/lib/gcc/x86_64-linux-gnu/9/:/usr/lib/gcc/x86_64-linux-gnu/9/:/usr/lib/gcc/x86_64-linux-gnu/:/usr/lib/gcc/x86_64-linux-gnu/9/:/usr/lib/gcc/x86_64-linux-gnu/
        LIBRARY_PATH=/home/<USER>/software/vcpkg/vcpkg/installed/x64-linux/lib/../lib/:/home/<USER>/software/vcpkg/vcpkg/installed/x64-linux/lib/../lib/:/usr/lib/gcc/x86_64-linux-gnu/9/:/usr/lib/gcc/x86_64-linux-gnu/9/../../../x86_64-linux-gnu/:/usr/lib/gcc/x86_64-linux-gnu/9/../../../../lib/:/lib/x86_64-linux-gnu/:/lib/../lib/:/usr/lib/x86_64-linux-gnu/:/usr/lib/../lib/:/home/<USER>/software/vcpkg/vcpkg/installed/x64-linux/lib/:/home/<USER>/software/vcpkg/vcpkg/installed/x64-linux/lib/:./:/usr/lib/gcc/x86_64-linux-gnu/9/../../../:/lib/:/usr/lib/
        COLLECT_GCC_OPTIONS='-v' '-o' 'cmTC_6f10b' '-mtune=generic' '-march=x86-64'
         /usr/lib/gcc/x86_64-linux-gnu/9/collect2 -plugin /usr/lib/gcc/x86_64-linux-gnu/9/liblto_plugin.so -plugin-opt=/usr/lib/gcc/x86_64-linux-gnu/9/lto-wrapper -plugin-opt=-fresolution=/tmp/ccb9xCHO.res -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lc -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lgcc_s --build-id --eh-frame-hdr -m elf_x86_64 --hash-style=gnu --as-needed -dynamic-linker /lib64/ld-linux-x86-64.so.2 -pie -z now -z relro -o cmTC_6f10b /usr/lib/gcc/x86_64-linux-gnu/9/../../../x86_64-linux-gnu/Scrt1.o /usr/lib/gcc/x86_64-linux-gnu/9/../../../x86_64-linux-gnu/crti.o /usr/lib/gcc/x86_64-linux-gnu/9/crtbeginS.o -L/home/<USER>/software/vcpkg/vcpkg/installed/x64-linux/lib/../lib -L/home/<USER>/software/vcpkg/vcpkg/installed/x64-linux/lib/../lib -L/usr/lib/gcc/x86_64-linux-gnu/9 -L/usr/lib/gcc/x86_64-linux-gnu/9/../../../x86_64-linux-gnu -L/usr/lib/gcc/x86_64-linux-gnu/9/../../../../lib -L/lib/x86_64-linux-gnu -L/lib/../lib -L/usr/lib/x86_64-linux-gnu -L/usr/lib/../lib -L/home/<USER>/software/vcpkg/vcpkg/installed/x64-linux/lib -L/home/<USER>/software/vcpkg/vcpkg/installed/x64-linux/lib -L. -L/usr/lib/gcc/x86_64-linux-gnu/9/../../.. CMakeFiles/cmTC_6f10b.dir/CMakeCCompilerABI.c.o -lgcc --push-state --as-needed -lgcc_s --pop-state -lc -lgcc --push-state --as-needed -lgcc_s --pop-state /usr/lib/gcc/x86_64-linux-gnu/9/crtendS.o /usr/lib/gcc/x86_64-linux-gnu/9/../../../x86_64-linux-gnu/crtn.o
        COLLECT_GCC_OPTIONS='-v' '-o' 'cmTC_6f10b' '-mtune=generic' '-march=x86-64'
        make[1]: Leaving directory '/home/<USER>/xhttp/build/CMakeFiles/CMakeScratch/TryCompile-jsbfrE'
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "/usr/share/cmake-3.27/Modules/CMakeDetermineCompilerABI.cmake:127 (message)"
      - "/usr/share/cmake-3.27/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:4 (project)"
    message: |
      Parsed C implicit include dir info: rv=done
        found start of include info
        found start of implicit include info
          add: [/home/<USER>/software/vcpkg/vcpkg/installed/x64-linux/include]
          add: [.]
          add: [/usr/lib/gcc/x86_64-linux-gnu/9/include]
          add: [/usr/local/include]
          add: [/usr/include/x86_64-linux-gnu]
          add: [/usr/include]
        end of search list found
        collapse include dir [/home/<USER>/software/vcpkg/vcpkg/installed/x64-linux/include] ==> [/home/<USER>/software/vcpkg/vcpkg/installed/x64-linux/include]
        skipping relative include dir [.]
        collapse include dir [/usr/lib/gcc/x86_64-linux-gnu/9/include] ==> [/usr/lib/gcc/x86_64-linux-gnu/9/include]
        collapse include dir [/usr/local/include] ==> [/usr/local/include]
        collapse include dir [/usr/include/x86_64-linux-gnu] ==> [/usr/include/x86_64-linux-gnu]
        collapse include dir [/usr/include] ==> [/usr/include]
        implicit include dirs: [/home/<USER>/software/vcpkg/vcpkg/installed/x64-linux/include;/usr/lib/gcc/x86_64-linux-gnu/9/include;/usr/local/include;/usr/include/x86_64-linux-gnu;/usr/include]
      
      
  -
    kind: "message-v1"
    backtrace:
      - "/usr/share/cmake-3.27/Modules/CMakeDetermineCompilerABI.cmake:152 (message)"
      - "/usr/share/cmake-3.27/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:4 (project)"
    message: |
      Parsed C implicit link information:
        link line regex: [^( *|.*[/\\])(ld|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        ignore line: [Change Dir: '/home/<USER>/xhttp/build/CMakeFiles/CMakeScratch/TryCompile-jsbfrE']
        ignore line: []
        ignore line: [Run Build Command(s): /usr/bin/cmake -E env VERBOSE=1 /usr/bin/make -f Makefile cmTC_6f10b/fast]
        ignore line: [/usr/bin/make  -f CMakeFiles/cmTC_6f10b.dir/build.make CMakeFiles/cmTC_6f10b.dir/build]
        ignore line: [make[1]: Entering directory '/home/<USER>/xhttp/build/CMakeFiles/CMakeScratch/TryCompile-jsbfrE']
        ignore line: [Building C object CMakeFiles/cmTC_6f10b.dir/CMakeCCompilerABI.c.o]
        ignore line: [/usr/bin/cc   -v -o CMakeFiles/cmTC_6f10b.dir/CMakeCCompilerABI.c.o -c /usr/share/cmake-3.27/Modules/CMakeCCompilerABI.c]
        ignore line: [Using built-in specs.]
        ignore line: [COLLECT_GCC=/usr/bin/cc]
        ignore line: [OFFLOAD_TARGET_NAMES=nvptx-none:hsa]
        ignore line: [OFFLOAD_TARGET_DEFAULT=1]
        ignore line: [Target: x86_64-linux-gnu]
        ignore line: [Configured with: ../src/configure -v --with-pkgversion='Ubuntu 9.4.0-1ubuntu1~20.04.2' --with-bugurl=file:///usr/share/doc/gcc-9/README.Bugs --enable-languages=c ada c++ go brig d fortran objc obj-c++ gm2 --prefix=/usr --with-gcc-major-version-only --program-suffix=-9 --program-prefix=x86_64-linux-gnu- --enable-shared --enable-linker-build-id --libexecdir=/usr/lib --without-included-gettext --enable-threads=posix --libdir=/usr/lib --enable-nls --enable-clocale=gnu --enable-libstdcxx-debug --enable-libstdcxx-time=yes --with-default-libstdcxx-abi=new --enable-gnu-unique-object --disable-vtable-verify --enable-plugin --enable-default-pie --with-system-zlib --with-target-system-zlib=auto --enable-objc-gc=auto --enable-multiarch --disable-werror --with-arch-32=i686 --with-abi=m64 --with-multilib-list=m32 m64 mx32 --enable-multilib --with-tune=generic --enable-offload-targets=nvptx-none=/build/gcc-9-9QDOt0/gcc-9-9.4.0/debian/tmp-nvptx/usr hsa --without-cuda-driver --enable-checking=release --build=x86_64-linux-gnu --host=x86_64-linux-gnu --target=x86_64-linux-gnu]
        ignore line: [Thread model: posix]
        ignore line: [gcc version 9.4.0 (Ubuntu 9.4.0-1ubuntu1~20.04.2) ]
        ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles/cmTC_6f10b.dir/CMakeCCompilerABI.c.o' '-c' '-mtune=generic' '-march=x86-64']
        ignore line: [ /usr/lib/gcc/x86_64-linux-gnu/9/cc1 -quiet -v -imultiarch x86_64-linux-gnu /usr/share/cmake-3.27/Modules/CMakeCCompilerABI.c -quiet -dumpbase CMakeCCompilerABI.c -mtune=generic -march=x86-64 -auxbase-strip CMakeFiles/cmTC_6f10b.dir/CMakeCCompilerABI.c.o -version -fasynchronous-unwind-tables -fstack-protector-strong -Wformat -Wformat-security -fstack-clash-protection -fcf-protection -o /tmp/ccuag7QK.s]
        ignore line: [GNU C17 (Ubuntu 9.4.0-1ubuntu1~20.04.2) version 9.4.0 (x86_64-linux-gnu)]
        ignore line: [	compiled by GNU C version 9.4.0  GMP version 6.2.0  MPFR version 4.0.2  MPC version 1.1.0  isl version isl-0.22.1-GMP]
        ignore line: []
        ignore line: [GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072]
        ignore line: [ignoring nonexistent directory "/usr/local/include/x86_64-linux-gnu"]
        ignore line: [ignoring nonexistent directory "/usr/lib/gcc/x86_64-linux-gnu/9/include-fixed"]
        ignore line: [ignoring nonexistent directory "/usr/lib/gcc/x86_64-linux-gnu/9/../../../../x86_64-linux-gnu/include"]
        ignore line: [ignoring duplicate directory "/home/<USER>/software/vcpkg/vcpkg/installed/x64-linux/include"]
        ignore line: [#include "..." search starts here:]
        ignore line: [#include <...> search starts here:]
        ignore line: [ /home/<USER>/software/vcpkg/vcpkg/installed/x64-linux/include]
        ignore line: [ .]
        ignore line: [ /usr/lib/gcc/x86_64-linux-gnu/9/include]
        ignore line: [ /usr/local/include]
        ignore line: [ /usr/include/x86_64-linux-gnu]
        ignore line: [ /usr/include]
        ignore line: [End of search list.]
        ignore line: [GNU C17 (Ubuntu 9.4.0-1ubuntu1~20.04.2) version 9.4.0 (x86_64-linux-gnu)]
        ignore line: [	compiled by GNU C version 9.4.0  GMP version 6.2.0  MPFR version 4.0.2  MPC version 1.1.0  isl version isl-0.22.1-GMP]
        ignore line: []
        ignore line: [GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072]
        ignore line: [Compiler executable checksum: 01da938ff5dc2163489aa33cb3b747a7]
        ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles/cmTC_6f10b.dir/CMakeCCompilerABI.c.o' '-c' '-mtune=generic' '-march=x86-64']
        ignore line: [ as -v --64 -o CMakeFiles/cmTC_6f10b.dir/CMakeCCompilerABI.c.o /tmp/ccuag7QK.s]
        ignore line: [GNU assembler version 2.34 (x86_64-linux-gnu) using BFD version (GNU Binutils for Ubuntu) 2.34]
        ignore line: [COMPILER_PATH=/usr/lib/gcc/x86_64-linux-gnu/9/:/usr/lib/gcc/x86_64-linux-gnu/9/:/usr/lib/gcc/x86_64-linux-gnu/:/usr/lib/gcc/x86_64-linux-gnu/9/:/usr/lib/gcc/x86_64-linux-gnu/]
        ignore line: [LIBRARY_PATH=/home/<USER>/software/vcpkg/vcpkg/installed/x64-linux/lib/../lib/:/home/<USER>/software/vcpkg/vcpkg/installed/x64-linux/lib/../lib/:/usr/lib/gcc/x86_64-linux-gnu/9/:/usr/lib/gcc/x86_64-linux-gnu/9/../../../x86_64-linux-gnu/:/usr/lib/gcc/x86_64-linux-gnu/9/../../../../lib/:/lib/x86_64-linux-gnu/:/lib/../lib/:/usr/lib/x86_64-linux-gnu/:/usr/lib/../lib/:/home/<USER>/software/vcpkg/vcpkg/installed/x64-linux/lib/:/home/<USER>/software/vcpkg/vcpkg/installed/x64-linux/lib/:./:/usr/lib/gcc/x86_64-linux-gnu/9/../../../:/lib/:/usr/lib/]
        ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles/cmTC_6f10b.dir/CMakeCCompilerABI.c.o' '-c' '-mtune=generic' '-march=x86-64']
        ignore line: [Linking C executable cmTC_6f10b]
        ignore line: [/usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_6f10b.dir/link.txt --verbose=1]
        ignore line: [/usr/bin/cc  -v CMakeFiles/cmTC_6f10b.dir/CMakeCCompilerABI.c.o -o cmTC_6f10b ]
        ignore line: [Using built-in specs.]
        ignore line: [COLLECT_GCC=/usr/bin/cc]
        ignore line: [COLLECT_LTO_WRAPPER=/usr/lib/gcc/x86_64-linux-gnu/9/lto-wrapper]
        ignore line: [OFFLOAD_TARGET_NAMES=nvptx-none:hsa]
        ignore line: [OFFLOAD_TARGET_DEFAULT=1]
        ignore line: [Target: x86_64-linux-gnu]
        ignore line: [Configured with: ../src/configure -v --with-pkgversion='Ubuntu 9.4.0-1ubuntu1~20.04.2' --with-bugurl=file:///usr/share/doc/gcc-9/README.Bugs --enable-languages=c ada c++ go brig d fortran objc obj-c++ gm2 --prefix=/usr --with-gcc-major-version-only --program-suffix=-9 --program-prefix=x86_64-linux-gnu- --enable-shared --enable-linker-build-id --libexecdir=/usr/lib --without-included-gettext --enable-threads=posix --libdir=/usr/lib --enable-nls --enable-clocale=gnu --enable-libstdcxx-debug --enable-libstdcxx-time=yes --with-default-libstdcxx-abi=new --enable-gnu-unique-object --disable-vtable-verify --enable-plugin --enable-default-pie --with-system-zlib --with-target-system-zlib=auto --enable-objc-gc=auto --enable-multiarch --disable-werror --with-arch-32=i686 --with-abi=m64 --with-multilib-list=m32 m64 mx32 --enable-multilib --with-tune=generic --enable-offload-targets=nvptx-none=/build/gcc-9-9QDOt0/gcc-9-9.4.0/debian/tmp-nvptx/usr hsa --without-cuda-driver --enable-checking=release --build=x86_64-linux-gnu --host=x86_64-linux-gnu --target=x86_64-linux-gnu]
        ignore line: [Thread model: posix]
        ignore line: [gcc version 9.4.0 (Ubuntu 9.4.0-1ubuntu1~20.04.2) ]
        ignore line: [COMPILER_PATH=/usr/lib/gcc/x86_64-linux-gnu/9/:/usr/lib/gcc/x86_64-linux-gnu/9/:/usr/lib/gcc/x86_64-linux-gnu/:/usr/lib/gcc/x86_64-linux-gnu/9/:/usr/lib/gcc/x86_64-linux-gnu/]
        ignore line: [LIBRARY_PATH=/home/<USER>/software/vcpkg/vcpkg/installed/x64-linux/lib/../lib/:/home/<USER>/software/vcpkg/vcpkg/installed/x64-linux/lib/../lib/:/usr/lib/gcc/x86_64-linux-gnu/9/:/usr/lib/gcc/x86_64-linux-gnu/9/../../../x86_64-linux-gnu/:/usr/lib/gcc/x86_64-linux-gnu/9/../../../../lib/:/lib/x86_64-linux-gnu/:/lib/../lib/:/usr/lib/x86_64-linux-gnu/:/usr/lib/../lib/:/home/<USER>/software/vcpkg/vcpkg/installed/x64-linux/lib/:/home/<USER>/software/vcpkg/vcpkg/installed/x64-linux/lib/:./:/usr/lib/gcc/x86_64-linux-gnu/9/../../../:/lib/:/usr/lib/]
        ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'cmTC_6f10b' '-mtune=generic' '-march=x86-64']
        link line: [ /usr/lib/gcc/x86_64-linux-gnu/9/collect2 -plugin /usr/lib/gcc/x86_64-linux-gnu/9/liblto_plugin.so -plugin-opt=/usr/lib/gcc/x86_64-linux-gnu/9/lto-wrapper -plugin-opt=-fresolution=/tmp/ccb9xCHO.res -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lc -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lgcc_s --build-id --eh-frame-hdr -m elf_x86_64 --hash-style=gnu --as-needed -dynamic-linker /lib64/ld-linux-x86-64.so.2 -pie -z now -z relro -o cmTC_6f10b /usr/lib/gcc/x86_64-linux-gnu/9/../../../x86_64-linux-gnu/Scrt1.o /usr/lib/gcc/x86_64-linux-gnu/9/../../../x86_64-linux-gnu/crti.o /usr/lib/gcc/x86_64-linux-gnu/9/crtbeginS.o -L/home/<USER>/software/vcpkg/vcpkg/installed/x64-linux/lib/../lib -L/home/<USER>/software/vcpkg/vcpkg/installed/x64-linux/lib/../lib -L/usr/lib/gcc/x86_64-linux-gnu/9 -L/usr/lib/gcc/x86_64-linux-gnu/9/../../../x86_64-linux-gnu -L/usr/lib/gcc/x86_64-linux-gnu/9/../../../../lib -L/lib/x86_64-linux-gnu -L/lib/../lib -L/usr/lib/x86_64-linux-gnu -L/usr/lib/../lib -L/home/<USER>/software/vcpkg/vcpkg/installed/x64-linux/lib -L/home/<USER>/software/vcpkg/vcpkg/installed/x64-linux/lib -L. -L/usr/lib/gcc/x86_64-linux-gnu/9/../../.. CMakeFiles/cmTC_6f10b.dir/CMakeCCompilerABI.c.o -lgcc --push-state --as-needed -lgcc_s --pop-state -lc -lgcc --push-state --as-needed -lgcc_s --pop-state /usr/lib/gcc/x86_64-linux-gnu/9/crtendS.o /usr/lib/gcc/x86_64-linux-gnu/9/../../../x86_64-linux-gnu/crtn.o]
          arg [/usr/lib/gcc/x86_64-linux-gnu/9/collect2] ==> ignore
          arg [-plugin] ==> ignore
          arg [/usr/lib/gcc/x86_64-linux-gnu/9/liblto_plugin.so] ==> ignore
          arg [-plugin-opt=/usr/lib/gcc/x86_64-linux-gnu/9/lto-wrapper] ==> ignore
          arg [-plugin-opt=-fresolution=/tmp/ccb9xCHO.res] ==> ignore
          arg [-plugin-opt=-pass-through=-lgcc] ==> ignore
          arg [-plugin-opt=-pass-through=-lgcc_s] ==> ignore
          arg [-plugin-opt=-pass-through=-lc] ==> ignore
          arg [-plugin-opt=-pass-through=-lgcc] ==> ignore
          arg [-plugin-opt=-pass-through=-lgcc_s] ==> ignore
          arg [--build-id] ==> ignore
          arg [--eh-frame-hdr] ==> ignore
          arg [-m] ==> ignore
          arg [elf_x86_64] ==> ignore
          arg [--hash-style=gnu] ==> ignore
          arg [--as-needed] ==> ignore
          arg [-dynamic-linker] ==> ignore
          arg [/lib64/ld-linux-x86-64.so.2] ==> ignore
          arg [-pie] ==> ignore
          arg [-znow] ==> ignore
          arg [-zrelro] ==> ignore
          arg [-o] ==> ignore
          arg [cmTC_6f10b] ==> ignore
          arg [/usr/lib/gcc/x86_64-linux-gnu/9/../../../x86_64-linux-gnu/Scrt1.o] ==> obj [/usr/lib/gcc/x86_64-linux-gnu/9/../../../x86_64-linux-gnu/Scrt1.o]
          arg [/usr/lib/gcc/x86_64-linux-gnu/9/../../../x86_64-linux-gnu/crti.o] ==> obj [/usr/lib/gcc/x86_64-linux-gnu/9/../../../x86_64-linux-gnu/crti.o]
          arg [/usr/lib/gcc/x86_64-linux-gnu/9/crtbeginS.o] ==> obj [/usr/lib/gcc/x86_64-linux-gnu/9/crtbeginS.o]
          arg [-L/home/<USER>/software/vcpkg/vcpkg/installed/x64-linux/lib/../lib] ==> dir [/home/<USER>/software/vcpkg/vcpkg/installed/x64-linux/lib/../lib]
          arg [-L/home/<USER>/software/vcpkg/vcpkg/installed/x64-linux/lib/../lib] ==> dir [/home/<USER>/software/vcpkg/vcpkg/installed/x64-linux/lib/../lib]
          arg [-L/usr/lib/gcc/x86_64-linux-gnu/9] ==> dir [/usr/lib/gcc/x86_64-linux-gnu/9]
          arg [-L/usr/lib/gcc/x86_64-linux-gnu/9/../../../x86_64-linux-gnu] ==> dir [/usr/lib/gcc/x86_64-linux-gnu/9/../../../x86_64-linux-gnu]
          arg [-L/usr/lib/gcc/x86_64-linux-gnu/9/../../../../lib] ==> dir [/usr/lib/gcc/x86_64-linux-gnu/9/../../../../lib]
          arg [-L/lib/x86_64-linux-gnu] ==> dir [/lib/x86_64-linux-gnu]
          arg [-L/lib/../lib] ==> dir [/lib/../lib]
          arg [-L/usr/lib/x86_64-linux-gnu] ==> dir [/usr/lib/x86_64-linux-gnu]
          arg [-L/usr/lib/../lib] ==> dir [/usr/lib/../lib]
          arg [-L/home/<USER>/software/vcpkg/vcpkg/installed/x64-linux/lib] ==> dir [/home/<USER>/software/vcpkg/vcpkg/installed/x64-linux/lib]
          arg [-L/home/<USER>/software/vcpkg/vcpkg/installed/x64-linux/lib] ==> dir [/home/<USER>/software/vcpkg/vcpkg/installed/x64-linux/lib]
          arg [-L.] ==> ignore
          arg [-L/usr/lib/gcc/x86_64-linux-gnu/9/../../..] ==> dir [/usr/lib/gcc/x86_64-linux-gnu/9/../../..]
          arg [CMakeFiles/cmTC_6f10b.dir/CMakeCCompilerABI.c.o] ==> ignore
          arg [-lgcc] ==> lib [gcc]
          arg [--push-state] ==> ignore
          arg [--as-needed] ==> ignore
          arg [-lgcc_s] ==> lib [gcc_s]
          arg [--pop-state] ==> ignore
          arg [-lc] ==> lib [c]
          arg [-lgcc] ==> lib [gcc]
          arg [--push-state] ==> ignore
          arg [--as-needed] ==> ignore
          arg [-lgcc_s] ==> lib [gcc_s]
          arg [--pop-state] ==> ignore
          arg [/usr/lib/gcc/x86_64-linux-gnu/9/crtendS.o] ==> obj [/usr/lib/gcc/x86_64-linux-gnu/9/crtendS.o]
          arg [/usr/lib/gcc/x86_64-linux-gnu/9/../../../x86_64-linux-gnu/crtn.o] ==> obj [/usr/lib/gcc/x86_64-linux-gnu/9/../../../x86_64-linux-gnu/crtn.o]
        collapse obj [/usr/lib/gcc/x86_64-linux-gnu/9/../../../x86_64-linux-gnu/Scrt1.o] ==> [/usr/lib/x86_64-linux-gnu/Scrt1.o]
        collapse obj [/usr/lib/gcc/x86_64-linux-gnu/9/../../../x86_64-linux-gnu/crti.o] ==> [/usr/lib/x86_64-linux-gnu/crti.o]
        collapse obj [/usr/lib/gcc/x86_64-linux-gnu/9/../../../x86_64-linux-gnu/crtn.o] ==> [/usr/lib/x86_64-linux-gnu/crtn.o]
        collapse library dir [/home/<USER>/software/vcpkg/vcpkg/installed/x64-linux/lib/../lib] ==> [/home/<USER>/software/vcpkg/vcpkg/installed/x64-linux/lib]
        collapse library dir [/home/<USER>/software/vcpkg/vcpkg/installed/x64-linux/lib/../lib] ==> [/home/<USER>/software/vcpkg/vcpkg/installed/x64-linux/lib]
        collapse library dir [/usr/lib/gcc/x86_64-linux-gnu/9] ==> [/usr/lib/gcc/x86_64-linux-gnu/9]
        collapse library dir [/usr/lib/gcc/x86_64-linux-gnu/9/../../../x86_64-linux-gnu] ==> [/usr/lib/x86_64-linux-gnu]
        collapse library dir [/usr/lib/gcc/x86_64-linux-gnu/9/../../../../lib] ==> [/usr/lib]
        collapse library dir [/lib/x86_64-linux-gnu] ==> [/lib/x86_64-linux-gnu]
        collapse library dir [/lib/../lib] ==> [/lib]
        collapse library dir [/usr/lib/x86_64-linux-gnu] ==> [/usr/lib/x86_64-linux-gnu]
        collapse library dir [/usr/lib/../lib] ==> [/usr/lib]
        collapse library dir [/home/<USER>/software/vcpkg/vcpkg/installed/x64-linux/lib] ==> [/home/<USER>/software/vcpkg/vcpkg/installed/x64-linux/lib]
        collapse library dir [/home/<USER>/software/vcpkg/vcpkg/installed/x64-linux/lib] ==> [/home/<USER>/software/vcpkg/vcpkg/installed/x64-linux/lib]
        collapse library dir [/usr/lib/gcc/x86_64-linux-gnu/9/../../..] ==> [/usr/lib]
        implicit libs: [gcc;gcc_s;c;gcc;gcc_s]
        implicit objs: [/usr/lib/x86_64-linux-gnu/Scrt1.o;/usr/lib/x86_64-linux-gnu/crti.o;/usr/lib/gcc/x86_64-linux-gnu/9/crtbeginS.o;/usr/lib/gcc/x86_64-linux-gnu/9/crtendS.o;/usr/lib/x86_64-linux-gnu/crtn.o]
        implicit dirs: [/home/<USER>/software/vcpkg/vcpkg/installed/x64-linux/lib;/usr/lib/gcc/x86_64-linux-gnu/9;/usr/lib/x86_64-linux-gnu;/usr/lib;/lib/x86_64-linux-gnu;/lib]
        implicit fwks: []
      
      
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/share/cmake-3.27/Modules/CMakeDetermineCompilerABI.cmake:57 (try_compile)"
      - "/usr/share/cmake-3.27/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:4 (project)"
    checks:
      - "Detecting CXX compiler ABI info"
    directories:
      source: "/home/<USER>/xhttp/build/CMakeFiles/CMakeScratch/TryCompile-RKT5AH"
      binary: "/home/<USER>/xhttp/build/CMakeFiles/CMakeScratch/TryCompile-RKT5AH"
    cmakeVariables:
      CMAKE_CXX_FLAGS: ""
      CMAKE_CXX_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
    buildResult:
      variable: "CMAKE_CXX_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: '/home/<USER>/xhttp/build/CMakeFiles/CMakeScratch/TryCompile-RKT5AH'
        
        Run Build Command(s): /usr/bin/cmake -E env VERBOSE=1 /usr/bin/make -f Makefile cmTC_e0d26/fast
        /usr/bin/make  -f CMakeFiles/cmTC_e0d26.dir/build.make CMakeFiles/cmTC_e0d26.dir/build
        make[1]: Entering directory '/home/<USER>/xhttp/build/CMakeFiles/CMakeScratch/TryCompile-RKT5AH'
        Building CXX object CMakeFiles/cmTC_e0d26.dir/CMakeCXXCompilerABI.cpp.o
        /usr/bin/c++   -v -o CMakeFiles/cmTC_e0d26.dir/CMakeCXXCompilerABI.cpp.o -c /usr/share/cmake-3.27/Modules/CMakeCXXCompilerABI.cpp
        Using built-in specs.
        COLLECT_GCC=/usr/bin/c++
        OFFLOAD_TARGET_NAMES=nvptx-none:hsa
        OFFLOAD_TARGET_DEFAULT=1
        Target: x86_64-linux-gnu
        Configured with: ../src/configure -v --with-pkgversion='Ubuntu 9.4.0-1ubuntu1~20.04.2' --with-bugurl=file:///usr/share/doc/gcc-9/README.Bugs --enable-languages=c,ada,c++,go,brig,d,fortran,objc,obj-c++,gm2 --prefix=/usr --with-gcc-major-version-only --program-suffix=-9 --program-prefix=x86_64-linux-gnu- --enable-shared --enable-linker-build-id --libexecdir=/usr/lib --without-included-gettext --enable-threads=posix --libdir=/usr/lib --enable-nls --enable-clocale=gnu --enable-libstdcxx-debug --enable-libstdcxx-time=yes --with-default-libstdcxx-abi=new --enable-gnu-unique-object --disable-vtable-verify --enable-plugin --enable-default-pie --with-system-zlib --with-target-system-zlib=auto --enable-objc-gc=auto --enable-multiarch --disable-werror --with-arch-32=i686 --with-abi=m64 --with-multilib-list=m32,m64,mx32 --enable-multilib --with-tune=generic --enable-offload-targets=nvptx-none=/build/gcc-9-9QDOt0/gcc-9-9.4.0/debian/tmp-nvptx/usr,hsa --without-cuda-driver --enable-checking=release --build=x86_64-linux-gnu --host=x86_64-linux-gnu --target=x86_64-linux-gnu
        Thread model: posix
        gcc version 9.4.0 (Ubuntu 9.4.0-1ubuntu1~20.04.2) 
        COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles/cmTC_e0d26.dir/CMakeCXXCompilerABI.cpp.o' '-c' '-shared-libgcc' '-mtune=generic' '-march=x86-64'
         /usr/lib/gcc/x86_64-linux-gnu/9/cc1plus -quiet -v -imultiarch x86_64-linux-gnu -D_GNU_SOURCE /usr/share/cmake-3.27/Modules/CMakeCXXCompilerABI.cpp -quiet -dumpbase CMakeCXXCompilerABI.cpp -mtune=generic -march=x86-64 -auxbase-strip CMakeFiles/cmTC_e0d26.dir/CMakeCXXCompilerABI.cpp.o -version -fasynchronous-unwind-tables -fstack-protector-strong -Wformat -Wformat-security -fstack-clash-protection -fcf-protection -o /tmp/cc1ivog0.s
        GNU C++14 (Ubuntu 9.4.0-1ubuntu1~20.04.2) version 9.4.0 (x86_64-linux-gnu)
        	compiled by GNU C version 9.4.0, GMP version 6.2.0, MPFR version 4.0.2, MPC version 1.1.0, isl version isl-0.22.1-GMP
        
        GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072
        ignoring duplicate directory "/usr/include/x86_64-linux-gnu/c++/9"
        ignoring nonexistent directory "/usr/local/include/x86_64-linux-gnu"
        ignoring nonexistent directory "/usr/lib/gcc/x86_64-linux-gnu/9/include-fixed"
        ignoring nonexistent directory "/usr/lib/gcc/x86_64-linux-gnu/9/../../../../x86_64-linux-gnu/include"
        ignoring duplicate directory "/home/<USER>/software/vcpkg/vcpkg/installed/x64-linux/include"
        #include "..." search starts here:
        #include <...> search starts here:
         /home/<USER>/software/vcpkg/vcpkg/installed/x64-linux/include
         .
         /usr/include/c++/9
         /usr/include/x86_64-linux-gnu/c++/9
         /usr/include/c++/9/backward
         /usr/lib/gcc/x86_64-linux-gnu/9/include
         /usr/local/include
         /usr/include/x86_64-linux-gnu
         /usr/include
        End of search list.
        GNU C++14 (Ubuntu 9.4.0-1ubuntu1~20.04.2) version 9.4.0 (x86_64-linux-gnu)
        	compiled by GNU C version 9.4.0, GMP version 6.2.0, MPFR version 4.0.2, MPC version 1.1.0, isl version isl-0.22.1-GMP
        
        GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072
        Compiler executable checksum: 3d1eba838554fa2348dba760e4770469
        COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles/cmTC_e0d26.dir/CMakeCXXCompilerABI.cpp.o' '-c' '-shared-libgcc' '-mtune=generic' '-march=x86-64'
         as -v --64 -o CMakeFiles/cmTC_e0d26.dir/CMakeCXXCompilerABI.cpp.o /tmp/cc1ivog0.s
        GNU assembler version 2.34 (x86_64-linux-gnu) using BFD version (GNU Binutils for Ubuntu) 2.34
        COMPILER_PATH=/usr/lib/gcc/x86_64-linux-gnu/9/:/usr/lib/gcc/x86_64-linux-gnu/9/:/usr/lib/gcc/x86_64-linux-gnu/:/usr/lib/gcc/x86_64-linux-gnu/9/:/usr/lib/gcc/x86_64-linux-gnu/
        LIBRARY_PATH=/home/<USER>/software/vcpkg/vcpkg/installed/x64-linux/lib/../lib/:/home/<USER>/software/vcpkg/vcpkg/installed/x64-linux/lib/../lib/:/usr/lib/gcc/x86_64-linux-gnu/9/:/usr/lib/gcc/x86_64-linux-gnu/9/../../../x86_64-linux-gnu/:/usr/lib/gcc/x86_64-linux-gnu/9/../../../../lib/:/lib/x86_64-linux-gnu/:/lib/../lib/:/usr/lib/x86_64-linux-gnu/:/usr/lib/../lib/:/home/<USER>/software/vcpkg/vcpkg/installed/x64-linux/lib/:/home/<USER>/software/vcpkg/vcpkg/installed/x64-linux/lib/:./:/usr/lib/gcc/x86_64-linux-gnu/9/../../../:/lib/:/usr/lib/
        COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles/cmTC_e0d26.dir/CMakeCXXCompilerABI.cpp.o' '-c' '-shared-libgcc' '-mtune=generic' '-march=x86-64'
        Linking CXX executable cmTC_e0d26
        /usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_e0d26.dir/link.txt --verbose=1
        /usr/bin/c++  -v CMakeFiles/cmTC_e0d26.dir/CMakeCXXCompilerABI.cpp.o -o cmTC_e0d26 
        Using built-in specs.
        COLLECT_GCC=/usr/bin/c++
        COLLECT_LTO_WRAPPER=/usr/lib/gcc/x86_64-linux-gnu/9/lto-wrapper
        OFFLOAD_TARGET_NAMES=nvptx-none:hsa
        OFFLOAD_TARGET_DEFAULT=1
        Target: x86_64-linux-gnu
        Configured with: ../src/configure -v --with-pkgversion='Ubuntu 9.4.0-1ubuntu1~20.04.2' --with-bugurl=file:///usr/share/doc/gcc-9/README.Bugs --enable-languages=c,ada,c++,go,brig,d,fortran,objc,obj-c++,gm2 --prefix=/usr --with-gcc-major-version-only --program-suffix=-9 --program-prefix=x86_64-linux-gnu- --enable-shared --enable-linker-build-id --libexecdir=/usr/lib --without-included-gettext --enable-threads=posix --libdir=/usr/lib --enable-nls --enable-clocale=gnu --enable-libstdcxx-debug --enable-libstdcxx-time=yes --with-default-libstdcxx-abi=new --enable-gnu-unique-object --disable-vtable-verify --enable-plugin --enable-default-pie --with-system-zlib --with-target-system-zlib=auto --enable-objc-gc=auto --enable-multiarch --disable-werror --with-arch-32=i686 --with-abi=m64 --with-multilib-list=m32,m64,mx32 --enable-multilib --with-tune=generic --enable-offload-targets=nvptx-none=/build/gcc-9-9QDOt0/gcc-9-9.4.0/debian/tmp-nvptx/usr,hsa --without-cuda-driver --enable-checking=release --build=x86_64-linux-gnu --host=x86_64-linux-gnu --target=x86_64-linux-gnu
        Thread model: posix
        gcc version 9.4.0 (Ubuntu 9.4.0-1ubuntu1~20.04.2) 
        COMPILER_PATH=/usr/lib/gcc/x86_64-linux-gnu/9/:/usr/lib/gcc/x86_64-linux-gnu/9/:/usr/lib/gcc/x86_64-linux-gnu/:/usr/lib/gcc/x86_64-linux-gnu/9/:/usr/lib/gcc/x86_64-linux-gnu/
        LIBRARY_PATH=/home/<USER>/software/vcpkg/vcpkg/installed/x64-linux/lib/../lib/:/home/<USER>/software/vcpkg/vcpkg/installed/x64-linux/lib/../lib/:/usr/lib/gcc/x86_64-linux-gnu/9/:/usr/lib/gcc/x86_64-linux-gnu/9/../../../x86_64-linux-gnu/:/usr/lib/gcc/x86_64-linux-gnu/9/../../../../lib/:/lib/x86_64-linux-gnu/:/lib/../lib/:/usr/lib/x86_64-linux-gnu/:/usr/lib/../lib/:/home/<USER>/software/vcpkg/vcpkg/installed/x64-linux/lib/:/home/<USER>/software/vcpkg/vcpkg/installed/x64-linux/lib/:./:/usr/lib/gcc/x86_64-linux-gnu/9/../../../:/lib/:/usr/lib/
        COLLECT_GCC_OPTIONS='-v' '-o' 'cmTC_e0d26' '-shared-libgcc' '-mtune=generic' '-march=x86-64'
         /usr/lib/gcc/x86_64-linux-gnu/9/collect2 -plugin /usr/lib/gcc/x86_64-linux-gnu/9/liblto_plugin.so -plugin-opt=/usr/lib/gcc/x86_64-linux-gnu/9/lto-wrapper -plugin-opt=-fresolution=/tmp/ccmehsHq.res -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lc -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lgcc --build-id --eh-frame-hdr -m elf_x86_64 --hash-style=gnu --as-needed -dynamic-linker /lib64/ld-linux-x86-64.so.2 -pie -z now -z relro -o cmTC_e0d26 /usr/lib/gcc/x86_64-linux-gnu/9/../../../x86_64-linux-gnu/Scrt1.o /usr/lib/gcc/x86_64-linux-gnu/9/../../../x86_64-linux-gnu/crti.o /usr/lib/gcc/x86_64-linux-gnu/9/crtbeginS.o -L/home/<USER>/software/vcpkg/vcpkg/installed/x64-linux/lib/../lib -L/home/<USER>/software/vcpkg/vcpkg/installed/x64-linux/lib/../lib -L/usr/lib/gcc/x86_64-linux-gnu/9 -L/usr/lib/gcc/x86_64-linux-gnu/9/../../../x86_64-linux-gnu -L/usr/lib/gcc/x86_64-linux-gnu/9/../../../../lib -L/lib/x86_64-linux-gnu -L/lib/../lib -L/usr/lib/x86_64-linux-gnu -L/usr/lib/../lib -L/home/<USER>/software/vcpkg/vcpkg/installed/x64-linux/lib -L/home/<USER>/software/vcpkg/vcpkg/installed/x64-linux/lib -L. -L/usr/lib/gcc/x86_64-linux-gnu/9/../../.. CMakeFiles/cmTC_e0d26.dir/CMakeCXXCompilerABI.cpp.o -lstdc++ -lm -lgcc_s -lgcc -lc -lgcc_s -lgcc /usr/lib/gcc/x86_64-linux-gnu/9/crtendS.o /usr/lib/gcc/x86_64-linux-gnu/9/../../../x86_64-linux-gnu/crtn.o
        COLLECT_GCC_OPTIONS='-v' '-o' 'cmTC_e0d26' '-shared-libgcc' '-mtune=generic' '-march=x86-64'
        make[1]: Leaving directory '/home/<USER>/xhttp/build/CMakeFiles/CMakeScratch/TryCompile-RKT5AH'
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "/usr/share/cmake-3.27/Modules/CMakeDetermineCompilerABI.cmake:127 (message)"
      - "/usr/share/cmake-3.27/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:4 (project)"
    message: |
      Parsed CXX implicit include dir info: rv=done
        found start of include info
        found start of implicit include info
          add: [/home/<USER>/software/vcpkg/vcpkg/installed/x64-linux/include]
          add: [.]
          add: [/usr/include/c++/9]
          add: [/usr/include/x86_64-linux-gnu/c++/9]
          add: [/usr/include/c++/9/backward]
          add: [/usr/lib/gcc/x86_64-linux-gnu/9/include]
          add: [/usr/local/include]
          add: [/usr/include/x86_64-linux-gnu]
          add: [/usr/include]
        end of search list found
        collapse include dir [/home/<USER>/software/vcpkg/vcpkg/installed/x64-linux/include] ==> [/home/<USER>/software/vcpkg/vcpkg/installed/x64-linux/include]
        skipping relative include dir [.]
        collapse include dir [/usr/include/c++/9] ==> [/usr/include/c++/9]
        collapse include dir [/usr/include/x86_64-linux-gnu/c++/9] ==> [/usr/include/x86_64-linux-gnu/c++/9]
        collapse include dir [/usr/include/c++/9/backward] ==> [/usr/include/c++/9/backward]
        collapse include dir [/usr/lib/gcc/x86_64-linux-gnu/9/include] ==> [/usr/lib/gcc/x86_64-linux-gnu/9/include]
        collapse include dir [/usr/local/include] ==> [/usr/local/include]
        collapse include dir [/usr/include/x86_64-linux-gnu] ==> [/usr/include/x86_64-linux-gnu]
        collapse include dir [/usr/include] ==> [/usr/include]
        implicit include dirs: [/home/<USER>/software/vcpkg/vcpkg/installed/x64-linux/include;/usr/include/c++/9;/usr/include/x86_64-linux-gnu/c++/9;/usr/include/c++/9/backward;/usr/lib/gcc/x86_64-linux-gnu/9/include;/usr/local/include;/usr/include/x86_64-linux-gnu;/usr/include]
      
      
  -
    kind: "message-v1"
    backtrace:
      - "/usr/share/cmake-3.27/Modules/CMakeDetermineCompilerABI.cmake:152 (message)"
      - "/usr/share/cmake-3.27/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:4 (project)"
    message: |
      Parsed CXX implicit link information:
        link line regex: [^( *|.*[/\\])(ld|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        ignore line: [Change Dir: '/home/<USER>/xhttp/build/CMakeFiles/CMakeScratch/TryCompile-RKT5AH']
        ignore line: []
        ignore line: [Run Build Command(s): /usr/bin/cmake -E env VERBOSE=1 /usr/bin/make -f Makefile cmTC_e0d26/fast]
        ignore line: [/usr/bin/make  -f CMakeFiles/cmTC_e0d26.dir/build.make CMakeFiles/cmTC_e0d26.dir/build]
        ignore line: [make[1]: Entering directory '/home/<USER>/xhttp/build/CMakeFiles/CMakeScratch/TryCompile-RKT5AH']
        ignore line: [Building CXX object CMakeFiles/cmTC_e0d26.dir/CMakeCXXCompilerABI.cpp.o]
        ignore line: [/usr/bin/c++   -v -o CMakeFiles/cmTC_e0d26.dir/CMakeCXXCompilerABI.cpp.o -c /usr/share/cmake-3.27/Modules/CMakeCXXCompilerABI.cpp]
        ignore line: [Using built-in specs.]
        ignore line: [COLLECT_GCC=/usr/bin/c++]
        ignore line: [OFFLOAD_TARGET_NAMES=nvptx-none:hsa]
        ignore line: [OFFLOAD_TARGET_DEFAULT=1]
        ignore line: [Target: x86_64-linux-gnu]
        ignore line: [Configured with: ../src/configure -v --with-pkgversion='Ubuntu 9.4.0-1ubuntu1~20.04.2' --with-bugurl=file:///usr/share/doc/gcc-9/README.Bugs --enable-languages=c ada c++ go brig d fortran objc obj-c++ gm2 --prefix=/usr --with-gcc-major-version-only --program-suffix=-9 --program-prefix=x86_64-linux-gnu- --enable-shared --enable-linker-build-id --libexecdir=/usr/lib --without-included-gettext --enable-threads=posix --libdir=/usr/lib --enable-nls --enable-clocale=gnu --enable-libstdcxx-debug --enable-libstdcxx-time=yes --with-default-libstdcxx-abi=new --enable-gnu-unique-object --disable-vtable-verify --enable-plugin --enable-default-pie --with-system-zlib --with-target-system-zlib=auto --enable-objc-gc=auto --enable-multiarch --disable-werror --with-arch-32=i686 --with-abi=m64 --with-multilib-list=m32 m64 mx32 --enable-multilib --with-tune=generic --enable-offload-targets=nvptx-none=/build/gcc-9-9QDOt0/gcc-9-9.4.0/debian/tmp-nvptx/usr hsa --without-cuda-driver --enable-checking=release --build=x86_64-linux-gnu --host=x86_64-linux-gnu --target=x86_64-linux-gnu]
        ignore line: [Thread model: posix]
        ignore line: [gcc version 9.4.0 (Ubuntu 9.4.0-1ubuntu1~20.04.2) ]
        ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles/cmTC_e0d26.dir/CMakeCXXCompilerABI.cpp.o' '-c' '-shared-libgcc' '-mtune=generic' '-march=x86-64']
        ignore line: [ /usr/lib/gcc/x86_64-linux-gnu/9/cc1plus -quiet -v -imultiarch x86_64-linux-gnu -D_GNU_SOURCE /usr/share/cmake-3.27/Modules/CMakeCXXCompilerABI.cpp -quiet -dumpbase CMakeCXXCompilerABI.cpp -mtune=generic -march=x86-64 -auxbase-strip CMakeFiles/cmTC_e0d26.dir/CMakeCXXCompilerABI.cpp.o -version -fasynchronous-unwind-tables -fstack-protector-strong -Wformat -Wformat-security -fstack-clash-protection -fcf-protection -o /tmp/cc1ivog0.s]
        ignore line: [GNU C++14 (Ubuntu 9.4.0-1ubuntu1~20.04.2) version 9.4.0 (x86_64-linux-gnu)]
        ignore line: [	compiled by GNU C version 9.4.0  GMP version 6.2.0  MPFR version 4.0.2  MPC version 1.1.0  isl version isl-0.22.1-GMP]
        ignore line: []
        ignore line: [GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072]
        ignore line: [ignoring duplicate directory "/usr/include/x86_64-linux-gnu/c++/9"]
        ignore line: [ignoring nonexistent directory "/usr/local/include/x86_64-linux-gnu"]
        ignore line: [ignoring nonexistent directory "/usr/lib/gcc/x86_64-linux-gnu/9/include-fixed"]
        ignore line: [ignoring nonexistent directory "/usr/lib/gcc/x86_64-linux-gnu/9/../../../../x86_64-linux-gnu/include"]
        ignore line: [ignoring duplicate directory "/home/<USER>/software/vcpkg/vcpkg/installed/x64-linux/include"]
        ignore line: [#include "..." search starts here:]
        ignore line: [#include <...> search starts here:]
        ignore line: [ /home/<USER>/software/vcpkg/vcpkg/installed/x64-linux/include]
        ignore line: [ .]
        ignore line: [ /usr/include/c++/9]
        ignore line: [ /usr/include/x86_64-linux-gnu/c++/9]
        ignore line: [ /usr/include/c++/9/backward]
        ignore line: [ /usr/lib/gcc/x86_64-linux-gnu/9/include]
        ignore line: [ /usr/local/include]
        ignore line: [ /usr/include/x86_64-linux-gnu]
        ignore line: [ /usr/include]
        ignore line: [End of search list.]
        ignore line: [GNU C++14 (Ubuntu 9.4.0-1ubuntu1~20.04.2) version 9.4.0 (x86_64-linux-gnu)]
        ignore line: [	compiled by GNU C version 9.4.0  GMP version 6.2.0  MPFR version 4.0.2  MPC version 1.1.0  isl version isl-0.22.1-GMP]
        ignore line: []
        ignore line: [GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072]
        ignore line: [Compiler executable checksum: 3d1eba838554fa2348dba760e4770469]
        ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles/cmTC_e0d26.dir/CMakeCXXCompilerABI.cpp.o' '-c' '-shared-libgcc' '-mtune=generic' '-march=x86-64']
        ignore line: [ as -v --64 -o CMakeFiles/cmTC_e0d26.dir/CMakeCXXCompilerABI.cpp.o /tmp/cc1ivog0.s]
        ignore line: [GNU assembler version 2.34 (x86_64-linux-gnu) using BFD version (GNU Binutils for Ubuntu) 2.34]
        ignore line: [COMPILER_PATH=/usr/lib/gcc/x86_64-linux-gnu/9/:/usr/lib/gcc/x86_64-linux-gnu/9/:/usr/lib/gcc/x86_64-linux-gnu/:/usr/lib/gcc/x86_64-linux-gnu/9/:/usr/lib/gcc/x86_64-linux-gnu/]
        ignore line: [LIBRARY_PATH=/home/<USER>/software/vcpkg/vcpkg/installed/x64-linux/lib/../lib/:/home/<USER>/software/vcpkg/vcpkg/installed/x64-linux/lib/../lib/:/usr/lib/gcc/x86_64-linux-gnu/9/:/usr/lib/gcc/x86_64-linux-gnu/9/../../../x86_64-linux-gnu/:/usr/lib/gcc/x86_64-linux-gnu/9/../../../../lib/:/lib/x86_64-linux-gnu/:/lib/../lib/:/usr/lib/x86_64-linux-gnu/:/usr/lib/../lib/:/home/<USER>/software/vcpkg/vcpkg/installed/x64-linux/lib/:/home/<USER>/software/vcpkg/vcpkg/installed/x64-linux/lib/:./:/usr/lib/gcc/x86_64-linux-gnu/9/../../../:/lib/:/usr/lib/]
        ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles/cmTC_e0d26.dir/CMakeCXXCompilerABI.cpp.o' '-c' '-shared-libgcc' '-mtune=generic' '-march=x86-64']
        ignore line: [Linking CXX executable cmTC_e0d26]
        ignore line: [/usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_e0d26.dir/link.txt --verbose=1]
        ignore line: [/usr/bin/c++  -v CMakeFiles/cmTC_e0d26.dir/CMakeCXXCompilerABI.cpp.o -o cmTC_e0d26 ]
        ignore line: [Using built-in specs.]
        ignore line: [COLLECT_GCC=/usr/bin/c++]
        ignore line: [COLLECT_LTO_WRAPPER=/usr/lib/gcc/x86_64-linux-gnu/9/lto-wrapper]
        ignore line: [OFFLOAD_TARGET_NAMES=nvptx-none:hsa]
        ignore line: [OFFLOAD_TARGET_DEFAULT=1]
        ignore line: [Target: x86_64-linux-gnu]
        ignore line: [Configured with: ../src/configure -v --with-pkgversion='Ubuntu 9.4.0-1ubuntu1~20.04.2' --with-bugurl=file:///usr/share/doc/gcc-9/README.Bugs --enable-languages=c ada c++ go brig d fortran objc obj-c++ gm2 --prefix=/usr --with-gcc-major-version-only --program-suffix=-9 --program-prefix=x86_64-linux-gnu- --enable-shared --enable-linker-build-id --libexecdir=/usr/lib --without-included-gettext --enable-threads=posix --libdir=/usr/lib --enable-nls --enable-clocale=gnu --enable-libstdcxx-debug --enable-libstdcxx-time=yes --with-default-libstdcxx-abi=new --enable-gnu-unique-object --disable-vtable-verify --enable-plugin --enable-default-pie --with-system-zlib --with-target-system-zlib=auto --enable-objc-gc=auto --enable-multiarch --disable-werror --with-arch-32=i686 --with-abi=m64 --with-multilib-list=m32 m64 mx32 --enable-multilib --with-tune=generic --enable-offload-targets=nvptx-none=/build/gcc-9-9QDOt0/gcc-9-9.4.0/debian/tmp-nvptx/usr hsa --without-cuda-driver --enable-checking=release --build=x86_64-linux-gnu --host=x86_64-linux-gnu --target=x86_64-linux-gnu]
        ignore line: [Thread model: posix]
        ignore line: [gcc version 9.4.0 (Ubuntu 9.4.0-1ubuntu1~20.04.2) ]
        ignore line: [COMPILER_PATH=/usr/lib/gcc/x86_64-linux-gnu/9/:/usr/lib/gcc/x86_64-linux-gnu/9/:/usr/lib/gcc/x86_64-linux-gnu/:/usr/lib/gcc/x86_64-linux-gnu/9/:/usr/lib/gcc/x86_64-linux-gnu/]
        ignore line: [LIBRARY_PATH=/home/<USER>/software/vcpkg/vcpkg/installed/x64-linux/lib/../lib/:/home/<USER>/software/vcpkg/vcpkg/installed/x64-linux/lib/../lib/:/usr/lib/gcc/x86_64-linux-gnu/9/:/usr/lib/gcc/x86_64-linux-gnu/9/../../../x86_64-linux-gnu/:/usr/lib/gcc/x86_64-linux-gnu/9/../../../../lib/:/lib/x86_64-linux-gnu/:/lib/../lib/:/usr/lib/x86_64-linux-gnu/:/usr/lib/../lib/:/home/<USER>/software/vcpkg/vcpkg/installed/x64-linux/lib/:/home/<USER>/software/vcpkg/vcpkg/installed/x64-linux/lib/:./:/usr/lib/gcc/x86_64-linux-gnu/9/../../../:/lib/:/usr/lib/]
        ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'cmTC_e0d26' '-shared-libgcc' '-mtune=generic' '-march=x86-64']
        link line: [ /usr/lib/gcc/x86_64-linux-gnu/9/collect2 -plugin /usr/lib/gcc/x86_64-linux-gnu/9/liblto_plugin.so -plugin-opt=/usr/lib/gcc/x86_64-linux-gnu/9/lto-wrapper -plugin-opt=-fresolution=/tmp/ccmehsHq.res -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lc -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lgcc --build-id --eh-frame-hdr -m elf_x86_64 --hash-style=gnu --as-needed -dynamic-linker /lib64/ld-linux-x86-64.so.2 -pie -z now -z relro -o cmTC_e0d26 /usr/lib/gcc/x86_64-linux-gnu/9/../../../x86_64-linux-gnu/Scrt1.o /usr/lib/gcc/x86_64-linux-gnu/9/../../../x86_64-linux-gnu/crti.o /usr/lib/gcc/x86_64-linux-gnu/9/crtbeginS.o -L/home/<USER>/software/vcpkg/vcpkg/installed/x64-linux/lib/../lib -L/home/<USER>/software/vcpkg/vcpkg/installed/x64-linux/lib/../lib -L/usr/lib/gcc/x86_64-linux-gnu/9 -L/usr/lib/gcc/x86_64-linux-gnu/9/../../../x86_64-linux-gnu -L/usr/lib/gcc/x86_64-linux-gnu/9/../../../../lib -L/lib/x86_64-linux-gnu -L/lib/../lib -L/usr/lib/x86_64-linux-gnu -L/usr/lib/../lib -L/home/<USER>/software/vcpkg/vcpkg/installed/x64-linux/lib -L/home/<USER>/software/vcpkg/vcpkg/installed/x64-linux/lib -L. -L/usr/lib/gcc/x86_64-linux-gnu/9/../../.. CMakeFiles/cmTC_e0d26.dir/CMakeCXXCompilerABI.cpp.o -lstdc++ -lm -lgcc_s -lgcc -lc -lgcc_s -lgcc /usr/lib/gcc/x86_64-linux-gnu/9/crtendS.o /usr/lib/gcc/x86_64-linux-gnu/9/../../../x86_64-linux-gnu/crtn.o]
          arg [/usr/lib/gcc/x86_64-linux-gnu/9/collect2] ==> ignore
          arg [-plugin] ==> ignore
          arg [/usr/lib/gcc/x86_64-linux-gnu/9/liblto_plugin.so] ==> ignore
          arg [-plugin-opt=/usr/lib/gcc/x86_64-linux-gnu/9/lto-wrapper] ==> ignore
          arg [-plugin-opt=-fresolution=/tmp/ccmehsHq.res] ==> ignore
          arg [-plugin-opt=-pass-through=-lgcc_s] ==> ignore
          arg [-plugin-opt=-pass-through=-lgcc] ==> ignore
          arg [-plugin-opt=-pass-through=-lc] ==> ignore
          arg [-plugin-opt=-pass-through=-lgcc_s] ==> ignore
          arg [-plugin-opt=-pass-through=-lgcc] ==> ignore
          arg [--build-id] ==> ignore
          arg [--eh-frame-hdr] ==> ignore
          arg [-m] ==> ignore
          arg [elf_x86_64] ==> ignore
          arg [--hash-style=gnu] ==> ignore
          arg [--as-needed] ==> ignore
          arg [-dynamic-linker] ==> ignore
          arg [/lib64/ld-linux-x86-64.so.2] ==> ignore
          arg [-pie] ==> ignore
          arg [-znow] ==> ignore
          arg [-zrelro] ==> ignore
          arg [-o] ==> ignore
          arg [cmTC_e0d26] ==> ignore
          arg [/usr/lib/gcc/x86_64-linux-gnu/9/../../../x86_64-linux-gnu/Scrt1.o] ==> obj [/usr/lib/gcc/x86_64-linux-gnu/9/../../../x86_64-linux-gnu/Scrt1.o]
          arg [/usr/lib/gcc/x86_64-linux-gnu/9/../../../x86_64-linux-gnu/crti.o] ==> obj [/usr/lib/gcc/x86_64-linux-gnu/9/../../../x86_64-linux-gnu/crti.o]
          arg [/usr/lib/gcc/x86_64-linux-gnu/9/crtbeginS.o] ==> obj [/usr/lib/gcc/x86_64-linux-gnu/9/crtbeginS.o]
          arg [-L/home/<USER>/software/vcpkg/vcpkg/installed/x64-linux/lib/../lib] ==> dir [/home/<USER>/software/vcpkg/vcpkg/installed/x64-linux/lib/../lib]
          arg [-L/home/<USER>/software/vcpkg/vcpkg/installed/x64-linux/lib/../lib] ==> dir [/home/<USER>/software/vcpkg/vcpkg/installed/x64-linux/lib/../lib]
          arg [-L/usr/lib/gcc/x86_64-linux-gnu/9] ==> dir [/usr/lib/gcc/x86_64-linux-gnu/9]
          arg [-L/usr/lib/gcc/x86_64-linux-gnu/9/../../../x86_64-linux-gnu] ==> dir [/usr/lib/gcc/x86_64-linux-gnu/9/../../../x86_64-linux-gnu]
          arg [-L/usr/lib/gcc/x86_64-linux-gnu/9/../../../../lib] ==> dir [/usr/lib/gcc/x86_64-linux-gnu/9/../../../../lib]
          arg [-L/lib/x86_64-linux-gnu] ==> dir [/lib/x86_64-linux-gnu]
          arg [-L/lib/../lib] ==> dir [/lib/../lib]
          arg [-L/usr/lib/x86_64-linux-gnu] ==> dir [/usr/lib/x86_64-linux-gnu]
          arg [-L/usr/lib/../lib] ==> dir [/usr/lib/../lib]
          arg [-L/home/<USER>/software/vcpkg/vcpkg/installed/x64-linux/lib] ==> dir [/home/<USER>/software/vcpkg/vcpkg/installed/x64-linux/lib]
          arg [-L/home/<USER>/software/vcpkg/vcpkg/installed/x64-linux/lib] ==> dir [/home/<USER>/software/vcpkg/vcpkg/installed/x64-linux/lib]
          arg [-L.] ==> ignore
          arg [-L/usr/lib/gcc/x86_64-linux-gnu/9/../../..] ==> dir [/usr/lib/gcc/x86_64-linux-gnu/9/../../..]
          arg [CMakeFiles/cmTC_e0d26.dir/CMakeCXXCompilerABI.cpp.o] ==> ignore
          arg [-lstdc++] ==> lib [stdc++]
          arg [-lm] ==> lib [m]
          arg [-lgcc_s] ==> lib [gcc_s]
          arg [-lgcc] ==> lib [gcc]
          arg [-lc] ==> lib [c]
          arg [-lgcc_s] ==> lib [gcc_s]
          arg [-lgcc] ==> lib [gcc]
          arg [/usr/lib/gcc/x86_64-linux-gnu/9/crtendS.o] ==> obj [/usr/lib/gcc/x86_64-linux-gnu/9/crtendS.o]
          arg [/usr/lib/gcc/x86_64-linux-gnu/9/../../../x86_64-linux-gnu/crtn.o] ==> obj [/usr/lib/gcc/x86_64-linux-gnu/9/../../../x86_64-linux-gnu/crtn.o]
        collapse obj [/usr/lib/gcc/x86_64-linux-gnu/9/../../../x86_64-linux-gnu/Scrt1.o] ==> [/usr/lib/x86_64-linux-gnu/Scrt1.o]
        collapse obj [/usr/lib/gcc/x86_64-linux-gnu/9/../../../x86_64-linux-gnu/crti.o] ==> [/usr/lib/x86_64-linux-gnu/crti.o]
        collapse obj [/usr/lib/gcc/x86_64-linux-gnu/9/../../../x86_64-linux-gnu/crtn.o] ==> [/usr/lib/x86_64-linux-gnu/crtn.o]
        collapse library dir [/home/<USER>/software/vcpkg/vcpkg/installed/x64-linux/lib/../lib] ==> [/home/<USER>/software/vcpkg/vcpkg/installed/x64-linux/lib]
        collapse library dir [/home/<USER>/software/vcpkg/vcpkg/installed/x64-linux/lib/../lib] ==> [/home/<USER>/software/vcpkg/vcpkg/installed/x64-linux/lib]
        collapse library dir [/usr/lib/gcc/x86_64-linux-gnu/9] ==> [/usr/lib/gcc/x86_64-linux-gnu/9]
        collapse library dir [/usr/lib/gcc/x86_64-linux-gnu/9/../../../x86_64-linux-gnu] ==> [/usr/lib/x86_64-linux-gnu]
        collapse library dir [/usr/lib/gcc/x86_64-linux-gnu/9/../../../../lib] ==> [/usr/lib]
        collapse library dir [/lib/x86_64-linux-gnu] ==> [/lib/x86_64-linux-gnu]
        collapse library dir [/lib/../lib] ==> [/lib]
        collapse library dir [/usr/lib/x86_64-linux-gnu] ==> [/usr/lib/x86_64-linux-gnu]
        collapse library dir [/usr/lib/../lib] ==> [/usr/lib]
        collapse library dir [/home/<USER>/software/vcpkg/vcpkg/installed/x64-linux/lib] ==> [/home/<USER>/software/vcpkg/vcpkg/installed/x64-linux/lib]
        collapse library dir [/home/<USER>/software/vcpkg/vcpkg/installed/x64-linux/lib] ==> [/home/<USER>/software/vcpkg/vcpkg/installed/x64-linux/lib]
        collapse library dir [/usr/lib/gcc/x86_64-linux-gnu/9/../../..] ==> [/usr/lib]
        implicit libs: [stdc++;m;gcc_s;gcc;c;gcc_s;gcc]
        implicit objs: [/usr/lib/x86_64-linux-gnu/Scrt1.o;/usr/lib/x86_64-linux-gnu/crti.o;/usr/lib/gcc/x86_64-linux-gnu/9/crtbeginS.o;/usr/lib/gcc/x86_64-linux-gnu/9/crtendS.o;/usr/lib/x86_64-linux-gnu/crtn.o]
        implicit dirs: [/home/<USER>/software/vcpkg/vcpkg/installed/x64-linux/lib;/usr/lib/gcc/x86_64-linux-gnu/9;/usr/lib/x86_64-linux-gnu;/usr/lib;/lib/x86_64-linux-gnu;/lib]
        implicit fwks: []
      
      
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/share/cmake-3.27/Modules/Internal/CheckSourceCompiles.cmake:101 (try_compile)"
      - "/usr/share/cmake-3.27/Modules/CheckCSourceCompiles.cmake:52 (cmake_check_source_compiles)"
      - "/usr/share/cmake-3.27/Modules/FindThreads.cmake:97 (CHECK_C_SOURCE_COMPILES)"
      - "/usr/share/cmake-3.27/Modules/FindThreads.cmake:163 (_threads_check_libc)"
      - "CMakeLists.txt:26 (find_package)"
    checks:
      - "Performing Test CMAKE_HAVE_LIBC_PTHREAD"
    directories:
      source: "/home/<USER>/xhttp/build/CMakeFiles/CMakeScratch/TryCompile-txm3JG"
      binary: "/home/<USER>/xhttp/build/CMakeFiles/CMakeScratch/TryCompile-txm3JG"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
    buildResult:
      variable: "CMAKE_HAVE_LIBC_PTHREAD"
      cached: true
      stdout: |
        Change Dir: '/home/<USER>/xhttp/build/CMakeFiles/CMakeScratch/TryCompile-txm3JG'
        
        Run Build Command(s): /usr/bin/cmake -E env VERBOSE=1 /usr/bin/make -f Makefile cmTC_5ec34/fast
        /usr/bin/make  -f CMakeFiles/cmTC_5ec34.dir/build.make CMakeFiles/cmTC_5ec34.dir/build
        make[1]: Entering directory '/home/<USER>/xhttp/build/CMakeFiles/CMakeScratch/TryCompile-txm3JG'
        Building C object CMakeFiles/cmTC_5ec34.dir/src.c.o
        /usr/bin/cc -DCMAKE_HAVE_LIBC_PTHREAD   -o CMakeFiles/cmTC_5ec34.dir/src.c.o -c /home/<USER>/xhttp/build/CMakeFiles/CMakeScratch/TryCompile-txm3JG/src.c
        Linking C executable cmTC_5ec34
        /usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_5ec34.dir/link.txt --verbose=1
        /usr/bin/cc CMakeFiles/cmTC_5ec34.dir/src.c.o -o cmTC_5ec34 
        /usr/bin/ld: CMakeFiles/cmTC_5ec34.dir/src.c.o: in function `main':
        src.c:(.text+0x46): undefined reference to `pthread_create'
        /usr/bin/ld: src.c:(.text+0x52): undefined reference to `pthread_detach'
        /usr/bin/ld: src.c:(.text+0x5e): undefined reference to `pthread_cancel'
        /usr/bin/ld: src.c:(.text+0x6f): undefined reference to `pthread_join'
        collect2: error: ld returned 1 exit status
        make[1]: *** [CMakeFiles/cmTC_5ec34.dir/build.make:99: cmTC_5ec34] Error 1
        make[1]: Leaving directory '/home/<USER>/xhttp/build/CMakeFiles/CMakeScratch/TryCompile-txm3JG'
        make: *** [Makefile:127: cmTC_5ec34/fast] Error 2
        
      exitCode: 2
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/share/cmake-3.27/Modules/CheckLibraryExists.cmake:69 (try_compile)"
      - "/usr/share/cmake-3.27/Modules/FindThreads.cmake:112 (CHECK_LIBRARY_EXISTS)"
      - "/usr/share/cmake-3.27/Modules/FindThreads.cmake:175 (_threads_check_lib)"
      - "CMakeLists.txt:26 (find_package)"
    checks:
      - "Looking for pthread_create in pthreads"
    directories:
      source: "/home/<USER>/xhttp/build/CMakeFiles/CMakeScratch/TryCompile-3RS0YF"
      binary: "/home/<USER>/xhttp/build/CMakeFiles/CMakeScratch/TryCompile-3RS0YF"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
    buildResult:
      variable: "CMAKE_HAVE_PTHREADS_CREATE"
      cached: true
      stdout: |
        Change Dir: '/home/<USER>/xhttp/build/CMakeFiles/CMakeScratch/TryCompile-3RS0YF'
        
        Run Build Command(s): /usr/bin/cmake -E env VERBOSE=1 /usr/bin/make -f Makefile cmTC_38f19/fast
        /usr/bin/make  -f CMakeFiles/cmTC_38f19.dir/build.make CMakeFiles/cmTC_38f19.dir/build
        make[1]: Entering directory '/home/<USER>/xhttp/build/CMakeFiles/CMakeScratch/TryCompile-3RS0YF'
        Building C object CMakeFiles/cmTC_38f19.dir/CheckFunctionExists.c.o
        /usr/bin/cc   -DCHECK_FUNCTION_EXISTS=pthread_create -o CMakeFiles/cmTC_38f19.dir/CheckFunctionExists.c.o -c /home/<USER>/xhttp/build/CMakeFiles/CMakeScratch/TryCompile-3RS0YF/CheckFunctionExists.c
        Linking C executable cmTC_38f19
        /usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_38f19.dir/link.txt --verbose=1
        /usr/bin/cc  -DCHECK_FUNCTION_EXISTS=pthread_create CMakeFiles/cmTC_38f19.dir/CheckFunctionExists.c.o -o cmTC_38f19  -lpthreads 
        /usr/bin/ld: cannot find -lpthreads
        collect2: error: ld returned 1 exit status
        make[1]: *** [CMakeFiles/cmTC_38f19.dir/build.make:99: cmTC_38f19] Error 1
        make[1]: Leaving directory '/home/<USER>/xhttp/build/CMakeFiles/CMakeScratch/TryCompile-3RS0YF'
        make: *** [Makefile:127: cmTC_38f19/fast] Error 2
        
      exitCode: 2
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/share/cmake-3.27/Modules/CheckLibraryExists.cmake:69 (try_compile)"
      - "/usr/share/cmake-3.27/Modules/FindThreads.cmake:112 (CHECK_LIBRARY_EXISTS)"
      - "/usr/share/cmake-3.27/Modules/FindThreads.cmake:176 (_threads_check_lib)"
      - "CMakeLists.txt:26 (find_package)"
    checks:
      - "Looking for pthread_create in pthread"
    directories:
      source: "/home/<USER>/xhttp/build/CMakeFiles/CMakeScratch/TryCompile-FNNKqH"
      binary: "/home/<USER>/xhttp/build/CMakeFiles/CMakeScratch/TryCompile-FNNKqH"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
    buildResult:
      variable: "CMAKE_HAVE_PTHREAD_CREATE"
      cached: true
      stdout: |
        Change Dir: '/home/<USER>/xhttp/build/CMakeFiles/CMakeScratch/TryCompile-FNNKqH'
        
        Run Build Command(s): /usr/bin/cmake -E env VERBOSE=1 /usr/bin/make -f Makefile cmTC_da7f7/fast
        /usr/bin/make  -f CMakeFiles/cmTC_da7f7.dir/build.make CMakeFiles/cmTC_da7f7.dir/build
        make[1]: Entering directory '/home/<USER>/xhttp/build/CMakeFiles/CMakeScratch/TryCompile-FNNKqH'
        Building C object CMakeFiles/cmTC_da7f7.dir/CheckFunctionExists.c.o
        /usr/bin/cc   -DCHECK_FUNCTION_EXISTS=pthread_create -o CMakeFiles/cmTC_da7f7.dir/CheckFunctionExists.c.o -c /home/<USER>/xhttp/build/CMakeFiles/CMakeScratch/TryCompile-FNNKqH/CheckFunctionExists.c
        Linking C executable cmTC_da7f7
        /usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_da7f7.dir/link.txt --verbose=1
        /usr/bin/cc  -DCHECK_FUNCTION_EXISTS=pthread_create CMakeFiles/cmTC_da7f7.dir/CheckFunctionExists.c.o -o cmTC_da7f7  -lpthread 
        make[1]: Leaving directory '/home/<USER>/xhttp/build/CMakeFiles/CMakeScratch/TryCompile-FNNKqH'
        
      exitCode: 0
...
