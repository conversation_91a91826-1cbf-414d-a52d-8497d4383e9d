{"cmake": {"generator": {"multiConfig": false, "name": "Unix Makefiles"}, "paths": {"cmake": "/usr/bin/cmake", "cpack": "/usr/bin/cpack", "ctest": "/usr/bin/ctest", "root": "/usr/share/cmake-3.27"}, "version": {"isDirty": false, "major": 3, "minor": 27, "patch": 9, "string": "3.27.9", "suffix": ""}}, "objects": [{"jsonFile": "codemodel-v2-16a8e0122cee42b2d902.json", "kind": "codemodel", "version": {"major": 2, "minor": 6}}, {"jsonFile": "cache-v2-b0c2b0e7593b1e21ef28.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-1b32991018d931b81a97.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}, {"jsonFile": "toolchains-v1-6a8a9870a9f70c2ebdc9.json", "kind": "toolchains", "version": {"major": 1, "minor": 0}}], "reply": {"client-vscode": {"query.json": {"requests": [{"kind": "cache", "version": 2}, {"kind": "codemodel", "version": 2}, {"kind": "toolchains", "version": 1}, {"kind": "cmakeFiles", "version": 1}], "responses": [{"jsonFile": "cache-v2-b0c2b0e7593b1e21ef28.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "codemodel-v2-16a8e0122cee42b2d902.json", "kind": "codemodel", "version": {"major": 2, "minor": 6}}, {"jsonFile": "toolchains-v1-6a8a9870a9f70c2ebdc9.json", "kind": "toolchains", "version": {"major": 1, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-1b32991018d931b81a97.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}]}, "query.json:Zone.Identifier": {"error": "unknown query file"}}}}