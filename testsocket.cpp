#include <iostream>
#include <string.h>
#include <thread>
#include "XTcp.h"
#include <regex>
#ifdef WIN32
#include <windows.h>
#else
#include <sys/epoll.h>
#endif

using namespace std;

class TcpThread {
public:
    void main() {
        char buf[10000] = {0}; // 清零缓冲区

        int recvLen = client.Recv(buf, sizeof(buf) - 1);
        if (recvLen <= 0) {
            client.Close();
            delete this;
            return;
        }

        cout << "========= Received Request =========\n"
             << buf << "\n==================================\n" << endl;

        // 解析HTTP请求获取文件路径
        string request(buf);
        string filename = "/";

        // 查找GET请求行
        size_t get_pos = request.find("GET ");
        if (get_pos != string::npos) {
            size_t path_start = get_pos + 4; // "GET " 后面
            size_t path_end = request.find(" ", path_start);
            if (path_end != string::npos) {
                filename = request.substr(path_start, path_end - path_start);
            }
        }

        // 如果请求根路径，默认返回index.html
        if (filename == "/") {
            filename = "/index.html";
        }

        string filepath = "www";
        filepath += filename;

        cout << "Requested file: " << filepath << endl;

        FILE *fp = fopen(filepath.c_str(), "rb");
        if (fp == NULL) {
            cout << "File not found: " << filepath << endl;

            // 发送404响应
            string error_msg = "HTTP/1.1 404 Not Found\r\n";
            error_msg += "Server: XHttp\r\n";
            error_msg += "Content-Type: text/html\r\n";
            error_msg += "Content-Length: 47\r\n";
            error_msg += "Connection: close\r\n\r\n";
            error_msg += "<html><body><h1>404 Not Found</h1></body></html>";

            client.Send(error_msg.c_str(), error_msg.size());
            client.Close();
            delete this;
            return;
        }

fseek(fp,0,SEEK_END);
int filesize = ftell(fp);
fseek(fp,0,0);
cout << "file size is " << filesize << endl;

char bsize[128] = {0};
sprintf(bsize,"%d",filesize);




        string rmsg = "";
        rmsg += "HTTP/1.1 200 OK\r\n";
        rmsg += "Server: XHttp\r\n";
        rmsg += "Content-Type: text/html\r\n";
        rmsg += "Content-Length: " ;
        rmsg += bsize;
        rmsg += "\r\n";
        rmsg += "Connection: close\r\n";  // 告诉浏览器关闭连接
        rmsg += "\r\n";                  // header 与 body 的分隔
     
        client.Send(rmsg.c_str(), rmsg.size());

        for(;;){
            int len = fread(buf, 1, sizeof(buf), fp);
            if (len <= 0) break;
            int re = client.Send(buf, len);
            if (re <= 0) break;
        }

        fclose(fp);  // 确保关闭文件
        client.Close();
        delete this;
    }

    XTcp client;
};

int main(int argc, char *argv[]) {
    unsigned short port = 8080;
    if (argc > 1) {
        port = atoi(argv[1]);
    }

    XTcp server;
    if (!server.Bind(port)) {
        cout << "Failed to bind port " << port << endl;
        return -1;
    }

    cout << "Server is running on http://127.0.0.1:" << port << endl;

#ifdef WIN32
    // Windows 使用简单的循环接受连接
    cout << "Running on Windows, using simple accept loop..." << endl;
    while (true) {
        XTcp client = server.Accept();
        if (client.sock > 0) {
            TcpThread* th = new TcpThread();
            th->client = client;
            thread t(&TcpThread::main, th);
            t.detach();
        }
    }
#else
    int epfd = epoll_create(256);
    if (epfd == -1) {
        perror("epoll_create failed");
        return -1;
    }

    epoll_event ev, events[20];
    ev.data.fd = server.sock;
    ev.events = EPOLLIN; // 使用 LT 模式（更简单）
    epoll_ctl(epfd, EPOLL_CTL_ADD, server.sock, &ev);

    while (true) {
        int count = epoll_wait(epfd, events, 20, 500);
        if (count < 0) {
            perror("epoll_wait error");
            break;
        }

        for (int i = 0; i < count; i++) {
            if (events[i].data.fd == server.sock) {
                // 新连接
                XTcp client = server.Accept();
                if (client.sock > 0) {
                    ev.data.fd = client.sock;
                    ev.events = EPOLLIN;
                    epoll_ctl(epfd, EPOLL_CTL_ADD, client.sock, &ev);
                }
            } else {
                // 有数据可读
                int client_sock = events[i].data.fd;

                // 创建线程处理
                TcpThread* th = new TcpThread();
                th->client.sock = client_sock;

                thread t(&TcpThread::main, th);
                t.detach();

                // 从 epoll 删除该 socket，避免重复触发
                epoll_ctl(epfd, EPOLL_CTL_DEL, client_sock, &ev);

            }
        }
          
    }

  
#endif

    server.Close();

#ifdef WIN32
    WSACleanup();
#endif

    return 0;
}